import axios from "axios";
import { ElMessage } from "element-plus";

const request = axios.create({
    baseURL: 'http://localhost:9090',
    timeout: 30000 // 后台接口超时时间
})

// request 拦截器
// 可以自请求发送前对请求做一些处理
request.interceptors.request.use(
    (config) => {
        config.headers['Content-Type'] = 'application/json;charset=utf-8';
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// response 拦截器
// 可以在接口响应后统一处理结果
request.interceptors.response.use(
    (response) => {
        let res = response.data;
        // 兼容服务端返回的字符串数据
        if (typeof res ==='string') {
            res = res? JSON.parse(res) : res;
        }
        return res;
    },
    (error) => {
        // 关键：先判断 error.response 是否存在
        if (error.response) {
            // 存在 response 的情况（服务器有返回，但状态码非 2xx）
            if (error.response.status === 404) {
                ElMessage.error('未找到请求接口');
            } else if (error.response.status === 500) {
                ElMessage.error('系统异常，请查看后端控制台报错');
            } else {
                console.error('请求错误：', error.message);
                ElMessage.error(`请求出错：${error.message}`);
            }
        } else {
            // 不存在 response 的情况（网络错误、超时等）
            console.error('网络异常或请求超时：', error.message);
            ElMessage.error('网络异常，请检查网络连接或稍后重试');
        }
        return Promise.reject(error);
    }
);

export default request;