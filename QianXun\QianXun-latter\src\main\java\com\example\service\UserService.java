package com.example.service;

import com.example.entity.User;
import com.example.exception.CustomException;
import com.example.mapper.UserMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserService {

    @Resource
    private UserMapper userMapper;
    public List<User> getUser() {
        return userMapper.getUser();
    }

    public User getUserBySno(Integer sno) {
        return userMapper.getUserBySno(sno);
    }

    public List<User> getUserlist(User user) {
        System.out.println(user);
        return null;
    }

    public PageInfo<User> getUserPage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<User> list = userMapper.getUser();
        return PageInfo.of(list);
    }

    public void add(User user) {
        userMapper.insert(user);
    }

    public void update(User user) {
        userMapper.update(user);
    }

    public void delete(Integer sno) {
        userMapper.delete(sno);
    }

    public void login(User user) {
        Integer sno = user.getSno(); //工号
        User dbuser = userMapper.getUserBySno(sno);
        if (dbuser == null) {
            throw new CustomException("400", "用户不存在");
        }
        String password = user.getPassword(); //密码
        if (!password.equals(dbuser.getPassword())) {
            throw new CustomException("500", "账号或密码错误"); // 改为400
        }

    }
}
