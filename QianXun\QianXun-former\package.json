{"name": "<PERSON>ian<PERSON><PERSON>-former", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "element-plus": "^2.10.4", "lucide-vue-next": "^0.533.0", "qiankun": "^2.10.16", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "vite": "^5.0.0"}}