<template>
  <div class="dashboard-container">
    <!-- 统计卡片区域 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="(stat, index) in stats" :key="index" @click="handleStatClick(stat)">
        <div class="stat-icon" :style="{ background: stat.color }">
          <i :class="stat.icon"></i>
        </div>
        <div class="stat-content">
          <h3>{{ stat.value }}</h3>
          <p>{{ stat.title }}</p>
          <span class="stat-change" :class="stat.trend">
            <i :class="stat.trend === 'up' ? 'el-icon-top' : 'el-icon-bottom'"></i>
            {{ stat.change }}
          </span>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <div class="chart-card">
        <div class="chart-header">
          <h3>知识增长趋势</h3>
          <div class="chart-controls">
            <el-button size="mini" :type="timeRange === 'day' ? 'primary' : 'text'" @click="setTimeRange('day')">日</el-button>
            <el-button size="mini" :type="timeRange === 'week' ? 'primary' : 'text'" @click="setTimeRange('week')">周</el-button>
            <el-button size="mini" :type="timeRange === 'month' ? 'primary' : 'text'" @click="setTimeRange('month')">月</el-button>
          </div>
        </div>
        <div class="chart-content">
          <div ref="lineChart" class="chart"></div>
        </div>
      </div>

      <div class="chart-card">
        <div class="chart-header">
          <h3>知识分类分布</h3>
        </div>
        <div class="chart-content">
          <div ref="pieChart" class="chart"></div>
        </div>
      </div>

      <div class="chart-card">
        <div class="chart-header">
          <h3>系统活跃度</h3>
        </div>
        <div class="chart-content">
          <div ref="barChart" class="chart"></div>
        </div>
      </div>

      <div class="chart-card">
        <div class="chart-header">
          <h3>实时监控</h3>
        </div>
        <div class="chart-content">
          <div class="monitor-grid">
            <div class="monitor-item">
              <div class="monitor-label">CPU使用率</div>
              <div class="monitor-progress">
                <el-progress :percentage="cpuUsage" :color="getProgressColor(cpuUsage)"></el-progress>
              </div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">内存使用率</div>
              <div class="monitor-progress">
                <el-progress :percentage="memoryUsage" :color="getProgressColor(memoryUsage)"></el-progress>
              </div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">磁盘使用率</div>
              <div class="monitor-progress">
                <el-progress :percentage="diskUsage" :color="getProgressColor(diskUsage)"></el-progress>
              </div>
            </div>
            <div class="monitor-item">
              <div class="monitor-label">网络流量</div>
              <div class="monitor-progress">
                <el-progress :percentage="networkUsage" :color="getProgressColor(networkUsage)"></el-progress>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="activity-section">
      <div class="activity-card">
        <div class="chart-header">
          <h3>最近活动</h3>
          <el-button size="mini" type="text" @click="viewAllActivities">查看全部</el-button>
        </div>
        <div class="activity-list">
          <div class="activity-item" v-for="(activity, index) in recentActivities" :key="index">
            <div class="activity-icon" :style="{ background: activity.color }">
              <i :class="activity.icon"></i>
            </div>
            <div class="activity-content">
              <p>{{ activity.description }}</p>
              <span class="activity-time">{{ activity.time }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataDashboard',
  data() {
    return {
      timeRange: 'month',
      stats: [
        {
          title: '总知识条目',
          value: '12,847',
          change: '+12.5%',
          trend: 'up',
          icon: 'el-icon-collection',
          color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        },
        {
          title: '今日新增',
          value: '156',
          change: '+8.2%',
          trend: 'up',
          icon: 'el-icon-plus',
          color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        },
        {
          title: '活跃用户',
          value: '2,341',
          change: '+5.7%',
          trend: 'up',
          icon: 'el-icon-user',
          color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
        },
        {
          title: '系统负载',
          value: '68%',
          change: '-2.1%',
          trend: 'down',
          icon: 'el-icon-monitor',
          color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
        }
      ],
      cpuUsage: 45,
      memoryUsage: 68,
      diskUsage: 32,
      networkUsage: 78,
      recentActivities: [
        {
          description: '用户张三上传了新的文档',
          time: '2分钟前',
          icon: 'el-icon-upload',
          color: '#4facfe'
        },
        {
          description: '知识抽取任务完成',
          time: '5分钟前',
          icon: 'el-icon-check',
          color: '#43e97b'
        },
        {
          description: '系统自动备份完成',
          time: '10分钟前',
          icon: 'el-icon-folder',
          color: '#667eea'
        },
        {
          description: '新用户李四注册',
          time: '15分钟前',
          icon: 'el-icon-user-solid',
          color: '#fa709a'
        }
      ]
    }
  },
  mounted() {
    this.initCharts();
    this.startRealTimeUpdate();
  },
  methods: {
    getProgressColor(percentage) {
      if (percentage < 50) return '#43e97b';
      if (percentage < 80) return '#fee140';
      return '#fa709a';
    },
    initCharts() {
      // 这里可以集成ECharts或其他图表库
      // 暂时用CSS创建简单的图表效果
      this.createMockCharts();
    },
    createMockCharts() {
      // 创建模拟图表
      setTimeout(() => {
        if (this.$refs.lineChart) {
          this.$refs.lineChart.innerHTML = '<div class="mock-line-chart"></div>';
        }
        if (this.$refs.pieChart) {
          this.$refs.pieChart.innerHTML = '<div class="mock-pie-chart"></div>';
        }
        if (this.$refs.barChart) {
          this.$refs.barChart.innerHTML = '<div class="mock-bar-chart"></div>';
        }
      }, 100);
    },
    startRealTimeUpdate() {
      setInterval(() => {
        this.cpuUsage = Math.floor(Math.random() * 100);
        this.memoryUsage = Math.floor(Math.random() * 100);
        this.diskUsage = Math.floor(Math.random() * 100);
        this.networkUsage = Math.floor(Math.random() * 100);
      }, 3000);
    },
    setTimeRange(range) {
      this.timeRange = range;
      this.$message.success(`已切换到${range === 'day' ? '日' : range === 'week' ? '周' : '月'}视图`);
      // 这里可以重新加载图表数据
    },
    viewAllActivities() {
      this.$message.info('活动详情页面开发中...');
    },
    handleStatClick(stat) {
      this.$message.success(`点击了${stat.title}，当前值：${stat.value}`);
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100%;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: white;
  font-size: 24px;
}

.stat-content h3 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: bold;
  color: #2c3e50;
}

.stat-content p {
  margin: 0 0 8px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.stat-change {
  font-size: 12px;
  font-weight: bold;
}

.stat-change.up {
  color: #27ae60;
}

.stat-change.down {
  color: #e74c3c;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  padding: 20px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.chart-content {
  padding: 20px 24px 24px;
}

.chart {
  height: 200px;
  position: relative;
}

.mock-line-chart,
.mock-pie-chart,
.mock-bar-chart {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  opacity: 0.1;
  position: relative;
}

.mock-line-chart::after {
  content: '📈';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  opacity: 0.3;
}

.mock-pie-chart::after {
  content: '📊';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  opacity: 0.3;
}

.mock-bar-chart::after {
  content: '📉';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 48px;
  opacity: 0.3;
}

.monitor-grid {
  display: grid;
  gap: 16px;
}

.monitor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitor-label {
  font-size: 14px;
  color: #7f8c8d;
  min-width: 80px;
}

.monitor-progress {
  flex: 1;
  margin-left: 16px;
}

.activity-section {
  display: grid;
  grid-template-columns: 1fr;
}

.activity-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.activity-list {
  padding: 0 24px 24px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 16px;
}

.activity-content p {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 14px;
}

.activity-time {
  color: #7f8c8d;
  font-size: 12px;
}
</style>
