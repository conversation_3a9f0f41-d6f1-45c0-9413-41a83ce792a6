<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">
    <select id="getUser" resultType="com.example.entity.User">
    select * from sys_user
    </select>
    <insert id="insert" parameterType="com.example.entity.User">
        insert into sys_user (sno, dept_id, nick_name, password, phonenumber, user_role, avatar)
        values (#{sno}, #{deptId}, #{nickName}, #{password}, #{phonenumber}, #{userRole}, #{avatar})
    </insert>

    <update id="update" parameterType="com.example.entity.User">
        update sys_user set nick_name = #{nickName}, password = #{password},
        phonenumber = #{phonenumber}, user_role = #{userRole}, avatar = #{avatar}
        where sno = #{sno}
    </update>

    <delete id="delete" parameterType="com.example.entity.User">
        delete from sys_user where sno = #{sno}
    </delete>
</mapper>