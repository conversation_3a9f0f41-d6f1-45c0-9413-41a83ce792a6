/* Hero Section Styles */
.hero-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: #000;
  margin: 0;
  padding: 0;
  top: 0;
  left: 0;
}

.hero-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.side-menu {
  position: fixed;
  top: 50px;
  left: 50px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.menu-icon {
  display: flex;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
}

.menu-icon span {
  width: 25px;
  height: 2px;
  background: white;
  transition: all 0.3s ease;
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  color: white;
  font-size: 14px;
  letter-spacing: 3px;
  font-weight: 300;
}

.hero-content {
  position: fixed;
  top: 75%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  color: white;
}

.hero-title {
  font-size: clamp(4rem, 12vw, 12rem);
  font-weight: 400; /* 从100改为700，更粗 */
  letter-spacing: 0.2em;
  margin: 0;
  line-height: 0.9;
  font-family: 'Arial', sans-serif;
}

.title-char {
  display: inline-block;
}

.hero-subtitle {
  margin-top: 2rem;
  font-size: clamp(1rem, 2vw, 1.5rem);
  font-weight: 300;
  letter-spacing: 0.1em;
  opacity: 0.8;
  line-height: 1.6;
  transform: translateX(-15px);
}

.subtitle-line {
  margin: 0.5rem 0;
}

.scroll-progress {
  position: fixed;
  bottom: 50px;
  right: 50px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: white;
}

.scroll-text {
  font-size: 12px;
  letter-spacing: 2px;
  font-weight: 300;
}

.progress-track {
  width: 2px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  position: relative;
}

.progress-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: white;
  transition: height 0.3s ease;
}

.section-counter {
  font-size: 14px;
  font-weight: 300;
  letter-spacing: 1px;
}

.scroll-sections {
  position: relative;
  z-index: 5;
}

.content-section {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  padding: 0 2rem;
}

.cosmos-style {
  background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

.cosmos-content {
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
}

.cosmos-content.section-1 {
  opacity: 1;
  transform: translate(-50%, -50%);
}

.cosmos-content.section-2 {
  opacity: 0;
  transform: translate(-50%, -60%);
  pointer-events: none;
}

.cosmos-content.section-2.active {
  opacity: 1;
  transform: translate(-50%, -50%);
  pointer-events: auto;
}

.cosmos-content.section-1.inactive {
  opacity: 0;
  transform: translate(-50%, -40%);
  pointer-events: none;
}

.cosmos-subtitle {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .side-menu {
    top: 30px;
    left: 30px;
  }
  
  .scroll-progress {
    bottom: 30px;
    right: 30px;
  }
  
  .hero-title {
    font-size: clamp(3rem, 15vw, 8rem);
  }
  
  .hero-subtitle {
    font-size: clamp(0.9rem, 4vw, 1.2rem);
    margin-top: 1.5rem;
  }
  
  .content-section {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: clamp(2.5rem, 18vw, 6rem);
    letter-spacing: 0.1em;
  }
  
  .hero-subtitle {
    font-size: clamp(0.8rem, 5vw, 1rem);
  }
  
  .side-menu {
    top: 20px;
    left: 20px;
  }
  
  .scroll-progress {
    bottom: 20px;
    right: 20px;
  }
  
  .progress-track {
    height: 60px;
  }
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  animation: fadeIn 1s ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.slide-up {
  transform: translateY(50px);
  opacity: 0;
  animation: slideUp 1s ease-out forwards;
}

@keyframes slideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100%;
  font-family: 'Arial', sans-serif;
  background: #000;
  overflow-x: hidden;
  overflow-y: auto;
}

