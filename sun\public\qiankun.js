// qiankun 生命周期钩子
let root;

function render(props = {}) {
  const { container } = props;
  // Next.js 应用已经在运行，我们只需要确保容器正确
  console.log('React 子应用挂载', props);
}

// 如果不是被 qiankun 加载，则正常启动
if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

// 导出 qiankun 生命周期钩子
window.sunApp = {
  async bootstrap() {
    console.log('React 子应用 bootstrap');
  },
  
  async mount(props) {
    console.log('React 子应用 mount', props);
    render(props);
  },
  
  async unmount(props) {
    console.log('React 子应用 unmount', props);
  }
};