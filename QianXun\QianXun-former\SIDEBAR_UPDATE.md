# 侧边栏更新说明

## 概述
已将原有的Element Plus菜单组件替换为自定义的侧边栏组件，实现了与参考样式相同的效果。

## 新特性

### 1. 悬停展开/收缩动画
- 侧边栏默认宽度为5em（80px）
- 鼠标悬停时自动展开到16em（256px）
- 使用CSS变量和cubic-bezier缓动函数实现平滑动画

### 2. 子菜单功能
- 支持多级子菜单
- 点击父菜单项展开/收缩子菜单
- 子菜单展开时显示下拉箭头
- 鼠标离开侧边栏时自动收缩所有子菜单

### 3. 用户头像区域
- 底部显示用户头像和用户名
- 点击头像显示下拉菜单
- 包含"个人中心"和"登出"选项
- 悬停效果和动画过渡

### 4. 猫咪图片
- 侧边栏底部显示可爱的猫咪图片
- 悬停时显示，收缩时隐藏
- 带有阴影和过渡效果

### 5. 响应式设计
- 移动端适配
- 小屏幕时隐藏子菜单和文字
- 保持核心功能可用

## 颜色方案
使用CSS变量定义颜色，便于主题切换：
- 主背景：`hsl(220, 13%, 18%)`
- 次要背景：`hsl(220, 13%, 15%)`
- 深色背景：`hsl(220, 13%, 12%)`
- 浅色背景：`hsl(220, 13%, 20%)`
- 白色：`hsl(0, 0%, 100%)`
- 灰色：`hsl(0, 0%, 50%)`

## 动画效果
- 主过渡：`0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)`
- 子菜单展开：`0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94)`
- 文字显示：`0.3s ease-out 0.1s`

## 使用方法
1. 侧边栏组件位于 `src/components/SideBar.vue`
2. 在主布局中引入并使用
3. 通过props传递当前激活的菜单项
4. 监听菜单选择事件进行路由跳转

## 兼容性
- 保持原有的菜单数据结构
- 兼容现有的路由配置
- 支持Element Plus的其他组件

## 文件结构
```
src/
├── components/
│   └── SideBar.vue          # 新的侧边栏组件
├── layouts/
│   └── MainLayout.vue       # 更新后的主布局
└── pages/
    └── Home.vue             # 更新的首页
```

## 注意事项
- 图标路径使用 `new URL()` 方式处理，确保在Vite中正确加载
- 所有动画都使用CSS实现，性能良好
- 保持了原有的功能逻辑和事件处理 