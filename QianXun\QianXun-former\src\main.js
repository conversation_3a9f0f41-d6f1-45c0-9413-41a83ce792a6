import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import router from './router'
import { registerMicroApps, start } from 'qiankun'

const app = createApp(App)

app.use(router)
app.use(ElementPlus)
app.mount('#app')

// 注册微应用
registerMicroApps([
  {
    name: 'sun-app',
    entry: '//localhost:3001/qiankun-entry.js', // React 子应用入口
    container: '#micro-container', // Vue 页面中挂载的 DOM
    activeRule: ['/react', '/'], // 触发路由：首页和/react路径
    props: {
      routerBase: '/', // 下发路由给子应用
    }
  }
])

// 启动 qiankun
start({
  prefetch: false, // 关闭预加载
  sandbox: {
    experimentalStyleIsolation: true // 样式隔离
  }
})
