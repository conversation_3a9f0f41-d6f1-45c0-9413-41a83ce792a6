package com.example.controller;

import com.example.common.Result;
import com.example.entity.User;
import com.example.service.UserService;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;
    //查询所有的用户
    @GetMapping("/getUser")
    public Result getUser(){
        List<User> list = userService.getUser();
        return Result.success(list);
    }
    //查询指定sno的用户
    @GetMapping("/getUserBySno/{sno}")
    public Result getUserBySno(@PathVariable Integer sno){
        User user = userService.getUserBySno(sno);
        return Result.success(user);
    }
    //分页查询指定sno的用户 pageNumber是当前页码
    @GetMapping("/getUserPage")
    public Result getUserPage(@RequestParam(defaultValue = "1") Integer pageNumber,
                              @RequestParam(defaultValue = "10") Integer pageSize){
        PageInfo<User> pageInfo = userService.getUserPage(pageNumber, pageSize);
        return Result.success(pageInfo);

    }
    //新增数据
    @PostMapping("/add")
    public Result add(@RequestBody User user){
        userService.add(user);
        return Result.success();
    }

    //修改数据
    @PutMapping("/update")
    public Result update(@RequestBody User user){
        userService.update(user);
        return Result.success();
    }

    //删除数据
    @DeleteMapping("/delete/{sno}")
    public Result delete(@PathVariable Integer sno){
        userService.delete(sno);
        return Result.success();
    }

    //登录
    @PostMapping("/login")
    public Result login(@RequestBody User user) {
        userService.login(user);
        return Result.success();
    }

}
