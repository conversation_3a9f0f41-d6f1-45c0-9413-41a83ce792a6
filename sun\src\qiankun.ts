// qiankun 生命周期钩子
import { createRoot, Root } from 'react-dom/client'
import Home from './app/page'
import './app/globals.css'

let root: Root | null = null

// 渲染函数
function render(props: any = {}) {
  const { container } = props
  const containerElement = container 
    ? container.querySelector('#root') 
    : document.getElementById('root')
  
  if (containerElement) {
    root = createRoot(containerElement)
    root.render(<Home />)
  }
}

// 独立运行时渲染
if (!(window as any).__POWERED_BY_QIANKUN__) {
  render()
}

// qiankun 生命周期钩子
export async function bootstrap() {
  console.log('React 子应用启动')
}

export async function mount(props: any) {
  console.log('React 子应用挂载', props)
  render(props)
}

export async function unmount(props: any) {
  console.log('React 子应用卸载', props)
  if (root) {
    root.unmount()
    root = null
  }
}