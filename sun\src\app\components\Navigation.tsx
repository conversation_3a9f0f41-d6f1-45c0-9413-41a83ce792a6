'use client'

import React from 'react'

export default function Navigation() {
  const navigateToVue = () => {
    // 检查是否在qiankun环境中
    if ((window as any).__POWERED_BY_QIANKUN__) {
      // 在微前端环境中，通过修改URL来导航
      window.history.pushState({}, '', '/vue')
      // 触发路由变化事件
      window.dispatchEvent(new PopStateEvent('popstate'))
    } else {
      // 独立运行时，跳转到Vue应用
      window.location.href = 'http://localhost:8888/login'
    }
  }

  return (
    <nav className="fixed top-4 right-4 z-50">
      <button
        onClick={navigateToVue}
        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-lg transition-colors duration-200"
      >
        进入 Vue 项目
      </button>
    </nav>
  )
}