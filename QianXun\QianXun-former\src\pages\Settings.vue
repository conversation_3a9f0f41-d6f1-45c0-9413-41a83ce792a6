<template>
  <div class="settings-page">
    <div class="settings-container">
      <div class="settings-sidebar">
        <div class="settings-header">
          <h3>个人信息</h3>
        </div>
        <div class="user-profile-card">
          <div class="avatar-section">
            <img src="@/assets/login/avatar.png" alt="用户头像" class="profile-avatar">
          </div>
          <div class="profile-info">
            <div class="info-item">
              <span class="label">用户名称</span>
              <span class="value">小君</span>
            </div>
            <div class="info-item">
              <span class="label">手机号码</span>
              <span class="value">18599545623</span>
            </div>
            <div class="info-item">
              <span class="label">用户邮箱</span>
              <span class="value"><EMAIL></span>
            </div>
            <div class="info-item">
              <span class="label">所属部门</span>
              <span class="value">超级管理员 / 老板</span>
            </div>
            <div class="info-item">
              <span class="label">所属角色</span>
              <span class="value">超级无敌管理员</span>
            </div>
            <div class="info-item">
              <span class="label">创建日期</span>
              <span class="value">2025.7.17 13:37:25</span>
            </div>
          </div>
        </div>
      </div>

      <div class="settings-content">
        <div class="settings-tabs">
          <div class="tab-item active">基本资料</div>
          <div class="tab-item">修改密码</div>
        </div>

        <div class="form-container">
          <div class="form-group">
            <label class="form-label required">用户名称</label>
            <el-input v-model="userForm.username" placeholder="小君" />
          </div>

          <div class="form-group">
            <label class="form-label required">手机号码</label>
            <el-input v-model="userForm.phone" placeholder="18599545623" />
          </div>

          <div class="form-group">
            <label class="form-label required">邮箱</label>
            <el-input v-model="userForm.email" placeholder="<EMAIL>" />
          </div>

          <div class="form-group">
            <label class="form-label required">性别</label>
            <div class="radio-group">
              <el-radio v-model="userForm.gender" label="male">男</el-radio>
              <el-radio v-model="userForm.gender" label="female">女</el-radio>
            </div>
          </div>

          <div class="form-actions">
            <el-button type="primary" @click="saveSettings">保存</el-button>
            <el-button @click="resetForm">关闭</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElInput, ElButton, ElRadio, ElMessage } from 'element-plus'

// 用户表单数据
const userForm = reactive({
  username: '小君',
  phone: '18599545623',
  email: '<EMAIL>',
  gender: 'male'
})

// 保存设置
const saveSettings = () => {
  console.log('保存用户设置:', userForm)
  ElMessage.success('设置保存成功！')
}

// 重置表单
const resetForm = () => {
  userForm.username = '小君'
  userForm.phone = '18599545623'
  userForm.email = '<EMAIL>'
  userForm.gender = 'male'
  ElMessage.info('表单已重置')
}
</script>

<style scoped>
.settings-page {
  padding: 24px;
  height: 100%;
  background: #f5f5f5;
  overflow-y: auto;
}

.settings-container {
  display: flex;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.settings-sidebar {
  width: 300px;
  flex-shrink: 0;
}

.settings-header {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.user-profile-card {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatar-section {
  text-align: center;
  margin-bottom: 24px;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e6e6e6;
}

.profile-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.info-item .value {
  font-size: 14px;
  color: #333;
  font-weight: 400;
}

.settings-content {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-tabs {
  display: flex;
  border-bottom: 1px solid #e6e6e6;
}

.tab-item {
  padding: 16px 24px;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab-item:hover {
  color: #222d82;
}

.tab-item.active {
  color: #222d82;
  border-bottom-color: #222d82;
  font-weight: 500;
}

.form-container {
  padding: 32px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-label.required::after {
  content: '*';
  color: #ff4d4f;
  margin-left: 4px;
}

.radio-group {
  display: flex;
  gap: 16px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e6e6e6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-page {
    padding: 16px;
  }
  
  .settings-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .settings-sidebar {
    width: 100%;
  }
  
  .form-container {
    padding: 20px;
  }
  
  .settings-tabs {
    overflow-x: auto;
  }
  
  .tab-item {
    white-space: nowrap;
    padding: 12px 20px;
  }
}
</style>