# 设计文档

## 概述

本设计文档描述了将现有Vue项目重构为使用全局Layout组件架构的详细方案。通过将侧边栏和顶部导航栏提取到MainLayout组件中，实现布局固定、内容区域动态切换的用户界面。

## 架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────┐
│                    MainLayout.vue                       │
├─────────────────────────────────────────────────────────┤
│  TopNav.vue (顶部导航栏)                                │
├─────────────────┬───────────────────────────────────────┤
│                 │                                       │
│  SideBar.vue    │         <router-view>                 │
│  (侧边栏)       │         (内容区域)                    │
│                 │                                       │
│                 │  ┌─────────────────────────────────┐  │
│                 │  │        Home.vue                 │  │
│                 │  │     DataCenter.vue              │  │
│                 │  │     Settings.vue                │  │
│                 │  │        ...                      │  │
│                 │  └─────────────────────────────────┘  │
└─────────────────┴───────────────────────────────────────┘
```

### 目录结构

```
src/
├── components/
│   ├── SideBar.vue      # 侧边栏组件
│   └── TopNav.vue       # 顶部导航组件
├── layouts/
│   └── MainLayout.vue   # 主布局组件
├── pages/
│   ├── Home.vue         # 首页内容
│   ├── DataCenter.vue   # 数据中心页面
│   └── Settings.vue     # 设置页面
└── router/
    └── index.js         # 路由配置
```

## 组件和接口

### 1. MainLayout.vue (主布局组件)

**职责：**
- 作为应用的外壳容器
- 包含TopNav和SideBar组件
- 提供router-view插槽用于内容切换
- 管理布局状态（如侧边栏折叠状态）

**Props：**
无

**Events：**
无

**主要功能：**
- 布局容器管理
- 响应式布局适配
- 全局状态管理（侧边栏折叠等）

### 2. TopNav.vue (顶部导航组件)

**职责：**
- 显示Logo区域
- 显示面包屑导航
- 显示用户信息和功能图标
- 管理标签页导航

**Props：**
```javascript
{
  isCollapsed: Boolean,        // 侧边栏折叠状态
  currentMenuInfo: Object,     // 当前菜单信息
  visitedTabs: Array          // 访问过的标签页
}
```

**Events：**
```javascript
{
  'toggle-collapse': void,     // 切换侧边栏折叠
  'refresh': void,            // 刷新内容
  'fullscreen': void,         // 切换全屏
  'tab-switch': Object,       // 切换标签页
  'tab-close': Object,        // 关闭标签页
  'user-logout': void,        // 用户退出
  'user-settings': void       // 用户设置
}
```

### 3. SideBar.vue (侧边栏组件)

**职责：**
- 显示导航菜单
- 处理菜单选择逻辑
- 管理菜单展开/收起状态
- 显示装饰元素（如猫咪图片）

**Props：**
```javascript
{
  isCollapsed: Boolean,        // 折叠状态
  activeMenu: String,         // 当前激活菜单
  menuItems: Array           // 菜单配置数据
}
```

**Events：**
```javascript
{
  'menu-select': String,      // 菜单选择
  'menu-open': String,        // 菜单展开
  'menu-close': String        // 菜单收起
}
```

### 4. 页面组件 (Home.vue, DataCenter.vue, Settings.vue)

**职责：**
- 只包含页面特定的内容
- 不包含任何布局相关的元素
- 专注于业务逻辑和数据展示

**结构：**
```vue
<template>
  <div class="page-container">
    <!-- 页面特定内容 -->
  </div>
</template>
```

## 数据模型

### 1. 菜单配置数据结构

```javascript
const menuItems = [
  {
    key: 'home',
    title: '首页',
    icon: 'home.svg',
    route: '/home'
  },
  {
    key: '1',
    title: '知识中心',
    icon: 'knowledge.svg',
    children: [
      {
        key: '1-1',
        title: '知识分类',
        route: '/knowledge/category'
      },
      {
        key: '1-2',
        title: '文件管理',
        route: '/knowledge/files'
      }
    ]
  }
  // ... 其他菜单项
]
```

### 2. 标签页数据结构

```javascript
const tabItem = {
  key: String,           // 唯一标识
  title: String,         // 显示标题
  parentTitle: String,   // 父级标题
  route: String,         // 路由路径
  closable: Boolean      // 是否可关闭
}
```

### 3. 当前菜单信息

```javascript
const currentMenuInfo = {
  title: String,         // 当前页面标题
  parentTitle: String,   // 父级标题
  breadcrumb: Array     // 面包屑路径
}
```

## 状态管理

### 全局状态

使用Vue 3的Composition API管理以下全局状态：

```javascript
// 布局状态
const layoutState = reactive({
  isCollapsed: false,        // 侧边栏折叠状态
  isFullscreen: false,       // 全屏状态
  isRefreshing: false        // 刷新状态
})

// 导航状态
const navigationState = reactive({
  activeMenu: 'home',        // 当前激活菜单
  currentMenuInfo: {},       // 当前菜单信息
  visitedTabs: [],          // 访问过的标签页
  menuItems: []             // 菜单配置
})

// 用户状态
const userState = reactive({
  userInfo: {},             // 用户信息
  showUserPopover: false    // 用户弹窗显示状态
})
```

### 状态传递方案

1. **Provide/Inject**: 用于跨组件层级的状态传递
2. **Props/Events**: 用于父子组件间的数据传递
3. **Composables**: 封装可复用的状态逻辑

## 路由设计

### 路由配置重构

```javascript
const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/login.vue'),
    meta: { layout: false } // 不使用MainLayout
  },
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: '',
        redirect: '/home'
      },
      {
        path: 'home',
        name: 'home',
        component: () => import('../pages/Home.vue'),
        meta: { 
          title: '首页',
          menuKey: 'home'
        }
      },
      {
        path: 'knowledge/center',
        name: 'knowledge-center',
        component: () => import('../pages/DataCenter.vue'),
        meta: { 
          title: '知识分类',
          parentTitle: '知识中心',
          menuKey: '1-1'
        }
      },
      // ... 其他路由
    ]
  }
]
```

### 路由守卫

```javascript
router.beforeEach((to, from, next) => {
  // 更新面包屑信息
  if (to.meta.title) {
    updateCurrentMenuInfo({
      title: to.meta.title,
      parentTitle: to.meta.parentTitle || '',
      menuKey: to.meta.menuKey
    })
  }
  
  // 添加到访问标签页
  if (to.meta.menuKey) {
    addVisitedTab({
      key: to.meta.menuKey,
      title: to.meta.title,
      parentTitle: to.meta.parentTitle,
      route: to.path
    })
  }
  
  next()
})
```

## 错误处理

### 1. 组件错误边界

```javascript
// ErrorBoundary.vue
export default {
  name: 'ErrorBoundary',
  data() {
    return {
      hasError: false,
      error: null
    }
  },
  errorCaptured(err, instance, info) {
    this.hasError = true
    this.error = err
    console.error('组件错误:', err, info)
    return false
  }
}
```

### 2. 路由错误处理

```javascript
router.onError((error) => {
  console.error('路由错误:', error)
  // 显示错误提示
  ElMessage.error('页面加载失败，请刷新重试')
})
```

### 3. 组件加载失败处理

```javascript
const AsyncComponent = defineAsyncComponent({
  loader: () => import('./Component.vue'),
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})
```

## 测试策略

### 1. 单元测试

**测试范围：**
- 各个组件的独立功能
- 状态管理逻辑
- 工具函数

**测试工具：**
- Vitest
- Vue Test Utils
- @testing-library/vue

### 2. 集成测试

**测试范围：**
- 组件间交互
- 路由跳转
- 状态传递

### 3. E2E测试

**测试范围：**
- 完整的用户操作流程
- 跨页面导航
- 布局响应式

**测试工具：**
- Cypress 或 Playwright

## 性能优化

### 1. 组件懒加载

```javascript
const Home = defineAsyncComponent(() => import('../pages/Home.vue'))
const DataCenter = defineAsyncComponent(() => import('../pages/DataCenter.vue'))
```

### 2. 路由懒加载

```javascript
{
  path: 'settings',
  component: () => import('../pages/Settings.vue')
}
```

### 3. 状态优化

- 使用`shallowRef`和`shallowReactive`减少深度响应
- 合理使用`computed`缓存计算结果
- 避免不必要的组件重渲染

### 4. 样式优化

- CSS模块化避免样式冲突
- 使用CSS变量统一主题
- 优化动画性能

## 兼容性考虑

### 1. 浏览器兼容性

- 支持现代浏览器（Chrome 88+, Firefox 85+, Safari 14+）
- 使用Babel转译确保ES6+语法兼容性

### 2. 响应式设计

- 移动端适配（768px以下）
- 平板适配（768px-1024px）
- 桌面端优化（1024px以上）

### 3. 无障碍访问

- 键盘导航支持
- 屏幕阅读器兼容
- 合适的颜色对比度

## 迁移策略

### 1. 渐进式迁移

1. 创建新的组件结构
2. 逐步提取现有代码到新组件
3. 保持现有功能不变
4. 测试验证后替换

### 2. 数据迁移

- 保持现有的状态管理逻辑
- 逐步重构为新的状态结构
- 确保数据流的一致性

### 3. 样式迁移

- 提取可复用的样式到独立文件
- 保持现有的视觉效果
- 优化CSS结构和命名