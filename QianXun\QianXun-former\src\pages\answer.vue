<template>
  <div class="qa-container">
    <!-- 左侧助理栏 -->
    <aside class="qa-sidebar">
      <div class="qa-sidebar-header">
        <span class="qa-sidebar-divider"></span>
        <span class="qa-sidebar-header-title">智能问答</span>
      </div>
      <el-button class="qa-new-assistant" type="primary" plain>
        <el-icon class="qa-icon"><User /></el-icon>
        新建助理
      </el-button>
      <div class="qa-assistant-list">
        <el-card class="qa-assistant-item is-active">
          <el-icon class="qa-icon"><Avatar /></el-icon>
          山君
        </el-card>
        <!-- 可扩展更多助理 -->
      </div>
    </aside>

    <!-- 中间聊天菜单栏 -->
    <section class="qa-chat-menu">
      <div class="qa-chat-menu-header">
        <span class="qa-chat-menu-title">聊天</span>
      </div>
      <el-button class="qa-new-topic" type="info" plain>
        <el-icon class="qa-icon"><Plus /></el-icon>
        打开新的话题
      </el-button>
      <div class="qa-topic-list">
        <el-card class="qa-topic-item is-active">
          neo4j的查询语言
        </el-card>
        <!-- 可扩展更多历史话题 -->
      </div>
    </section>

    <!-- 右侧聊天区 -->
    <main class="qa-chat-box">
      <div class="qa-dialog-box">
        <div class="qa-chat-history">
          <div class="qa-chat-message qa-chat-message--system">
            您好，我是您的智能助理，请问有什么可以帮助到您
          </div>
          <div class="qa-chat-message qa-chat-message--user">
            neo4j的查询语言
          </div>
          <el-tag class="qa-loading-tag" type="info">加载中...</el-tag>
        </div>
      </div>
      <div class="qa-input-area">
        <el-input
          class="qa-big-input"
          type="textarea"
          :rows="4"
          placeholder="消息概要助手..."
        />
        <div class="qa-input-actions">
          <el-button class="qa-cross-db-btn" type="info" plain>
            <el-icon class="qa-icon"><Search /></el-icon>
            跨库搜索
          </el-button>
          <el-icon class="qa-action-icon"><Scissor /></el-icon>
          <el-icon class="qa-action-icon"><Microphone /></el-icon>
          <el-icon class="qa-action-icon"><Promotion /></el-icon>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { User, Avatar, Plus, Search, Promotion, Scissor, Microphone } from '@element-plus/icons-vue'
</script>

<style scoped>
.qa-container {
  display: flex;
  height: 83.2vh;
  background: #f6f8fa;
  min-width: 1200px;
}

.qa-sidebar {
  width: 180px;
  background: #eaf1fb;
  padding: 24px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-right: 1.5px solid #e0e6ed;
}

.qa-sidebar-header {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  padding-left: 20px;
  gap: 8px;
  box-sizing: border-box;
}

.qa-sidebar-divider {
  display: inline-block;
  width: 4px;
  height: 22px;
  background: #3757e3; /* 这里可自定义颜色 */
}

.qa-sidebar-header-title {
  font-size: 18px;
  font-weight: bold;
  color: #000000;
  letter-spacing: 1px;
}

.qa-new-assistant {
  width: 140px;
  margin: 0 auto 32px auto;
  border-radius: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qa-assistant-list {
  width: 100%;
}
.qa-assistant-item {
  width: 140px;
  margin: 0 auto 16px auto;
  border-radius: 10px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: box-shadow 0.2s;
}
.qa-assistant-item.is-active {
  background: #fff;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  border: 1.5px solid #409eff;
}

.qa-icon {
  margin-right: 8px;
}

.qa-chat-menu {
  width: 240px;
  background: #f4f7fb;
  padding: 24px 0 0 0;
  border-right: 1.5px solid #e0e6ed;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 聊天菜单栏顶部标题样式 */
.qa-chat-menu-header {
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 8px;
  margin-bottom: 32px;
  margin-left: 60px;
  box-sizing: border-box;
}

.qa-chat-menu-title {
  font-size: 16px;
  font-weight: bold;
  color: #000000;
  letter-spacing: 1px;
}

.qa-new-topic {
  width: 180px;
  margin-bottom: 32px;
  border-radius: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qa-topic-list {
  width: 100%;
}
.qa-topic-item {
  width: 180px;
  margin: 0 auto 16px auto;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: box-shadow 0.2s;
}
.qa-topic-item.is-active {
  background: #fff;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  border: 1.5px solid #409eff;
}

.qa-chat-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f6f8fa;
  align-items: center;
  justify-content: flex-end;
  min-height: 0;
  height: 100%; /* 新增，确保撑满父容器 */
}
.qa-chat-history {
  flex: 1;
  padding: 32px 40px 16px 40px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.qa-chat-message {
  max-width: 60%;
  padding: 12px 20px;
  border-radius: 10px;
  font-size: 15px;
  line-height: 1.7;
  word-break: break-all;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
}
.qa-chat-message--system {
  align-self: flex-start;
  background: #e5e5e5;
  color: #666;
}
.qa-chat-message--user {
  align-self: flex-end;
  background: #c4d7ff;
  color: #000000;
}
.qa-loading-tag {
  align-self: flex-start;
  background: #c4d7ff;
  margin-top: 8px;
}
/* 对话框容器样式 */
.qa-dialog-box {
  width: 100%;
  max-width: 900px;
  margin: 40px auto 0 auto;
  background: #fff;
  /* border-radius: 18px; */
  /* box-shadow: 0 4px 24px 0 rgba(0,0,0,0.08); */
  padding: 32px 40px 16px 40px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1 1 auto; /* 关键：自动填满剩余空间 */
  overflow-y: auto;
}

.qa-input-area {
  width: 100%;
  max-width: 900px;
  background: #fff;
  /* border-radius: 16px; */
  /* box-shadow: 0 4px 24px 0 rgba(0,0,0,0.08); */
  padding: 32px 32px 16px 32px;
  margin: 0 auto; /* 关键：去掉 margin-bottom 或 margin: 0 auto 32px auto; */
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.qa-big-input {
  width: 100%;
  min-height: 80px;
  font-size: 18px;
  /* border-radius: 10px; */
  resize: none;
}

.qa-input-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
}

.qa-cross-db-btn {
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.qa-action-icon {
  font-size: 22px;
  color: #409eff;
  cursor: pointer;
  transition: color 0.2s;
}
.qa-action-icon:hover {
  color: #66b1ff;
}
@media (max-width: 1400px) {
  .qa-container { min-width: 1000px; }
  .qa-chat-box { padding: 0; }
}
@media (max-width: 900px) {
  .qa-container { flex-direction: column; min-width: 0; }
  .qa-sidebar, .qa-chat-menu { width: 100%; flex-direction: row; border-right: none; border-bottom: 1.5px solid #e0e6ed; }
  .qa-chat-box { flex: 1; }
}

</style>
