<template>
  <div class="react-container">
    <div id="micro-container"></div>
  </div>
</template>

<script>
export default {
  name: 'ReactContainer',
  mounted() {
    console.log('React 容器页面已挂载');
  },
  beforeUnmount() {
    console.log('React 容器页面即将卸载');
  }
}
</script>

<style scoped>
.react-container {
  width: 100%;
  height: 100vh;
}

#micro-container {
  width: 100%;
  height: 100%;
}
</style>