// 简化的 qiankun 入口文件
let root = null;

// 渲染函数
function render(props = {}) {
  const { container } = props;
  const targetContainer = container ? container.querySelector('#root') : document.getElementById('root');
  
  if (targetContainer) {
    // 创建 iframe 来加载 Next.js 应用
    const iframe = document.createElement('iframe');
    iframe.src = 'http://localhost:3001';
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';
    iframe.style.overflow = 'hidden';
    
    targetContainer.innerHTML = '';
    targetContainer.appendChild(iframe);
    root = iframe;
  }
}

// 独立运行时渲染
if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

// qiankun 生命周期钩子
window['sun-app'] = {
  async bootstrap() {
    console.log('React 子应用启动');
  },
  
  async mount(props) {
    console.log('React 子应用挂载', props);
    render(props);
  },
  
  async unmount(props) {
    console.log('React 子应用卸载', props);
    if (root && root.parentNode) {
      root.parentNode.removeChild(root);
      root = null;
    }
  }
};