
<template>
  <div id="app">
    <!-- 侧边栏 -->
    <nav id="navbar">
      <div class="sidebar-logo">
        <img src="/src/assets/icon/logo.png" alt="logo" />
      </div>
      <ul class="navbar-items flexbox-col">
        <!-- 这里不要再有 .navbar-logo 相关的 li -->
        <!-- 只保留菜单项 -->
        <li
          v-for="(item, idx) in navItems"
          :key="idx"
          class="navbar-item flexbox-left"
          :class="{ 'has-submenu': item.submenu }"
        >
          <!-- 主导航项 -->
          <div
            v-if="item.submenu"
            @click="toggleSubmenu(item.path)"
            class="navbar-item-inner flexbox-left"
            :class="{ 'expanded': expandedMenus.includes(item.path) }"
          >
            <div class="navbar-item-inner-icon-wrapper flexbox">
              <component :is="item.icon" :size="20" />
            </div>
            <span class="link-text">{{ item.text }}</span>
            <span class="submenu-arrow" :class="{ 'rotated': expandedMenus.includes(item.path) }">
              ▼
            </span>
          </div>
          
          <!-- 普通导航项 -->
          <router-link
            v-else
            :to="item.path"
            class="navbar-item-inner flexbox-left"
            active-class="active"
          >
            <div class="navbar-item-inner-icon-wrapper flexbox">
              <component :is="item.icon" :size="20" />
            </div>
            <span class="link-text">{{ item.text }}</span>
          </router-link>

          <!-- 子菜单 -->
          <ul 
            v-if="item.submenu" 
            class="submenu"
            :class="{ 'expanded': expandedMenus.includes(item.path) }"
          >
            <li 
              v-for="(subItem, subIdx) in item.submenu" 
              :key="subIdx"
              class="submenu-item"
            >
              <router-link
                :to="subItem.path"
                class="submenu-item-inner flexbox-left"
                active-class="active"
              >
                <div class="submenu-item-icon">
                  <component :is="subItem.icon" :size="16" />
                </div>
                <span class="submenu-link-text">{{ subItem.text }}</span>
              </router-link>
            </li>
          </ul>
        </li>
      </ul>

      <!-- 用户头像栏 -->
      <div class="user-profile" ref="userProfile">
        <div 
          class="user-profile-inner flexbox-left"
          @click="toggleUserMenu"
          :class="{ 'menu-active': showUserMenu }"
        >
          <div class="user-avatar">
            <img src="@/assets/login/avatar.png" alt="用户头像" />
          </div>
          <span class="user-name">排列课代表</span>
        </div>

        <!-- 用户下拉菜单 -->
        <div
          class="user-dropdown-menu"
          :class="{ 'show': showUserMenu }"
          ref="userDropdown"
        >
          <div class="dropdown-item" @click="handleLogout">
            <span>登出</span>
          </div>
        </div>
      </div>
    </nav>

  
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, onUnmounted } from 'vue'
import { Home, FolderOpen, PieChart, Users, MessageCircle, Settings, FileText, Folder, BookOpen, BookText, LibraryBig, ScanSearch, Layout, LayoutPanelLeft } from 'lucide-vue-next'

const expandedMenus = ref([])
const showUserMenu = ref(false)
const userProfile = ref(null)
const userDropdown = ref(null)
const navbar = ref(null)

const toggleSubmenu = (path) => {
  const index = expandedMenus.value.indexOf(path)
  if (index > -1) {
    expandedMenus.value.splice(index, 1)
  } else {
    expandedMenus.value.push(path)
  }
}

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const handlePersonalCenter = () => {
  console.log('Navigate to Personal Center')
  showUserMenu.value = false
  // Add navigation logic here
}

const handleLogout = () => {
  console.log('Logout user')
  showUserMenu.value = false
  // Add logout logic here
}

const handleClickOutside = (event) => {
  if (userProfile.value && !userProfile.value.contains(event.target)) {
    showUserMenu.value = false
  }
}

const handleNavbarMouseLeave = () => {
  // First stage: Collapse all expanded submenus
  expandedMenus.value = []
  
  // Second stage: Close user menu after submenu collapse animation
  setTimeout(() => {
    showUserMenu.value = false
  }, 100)
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  const navbarElement = document.getElementById('navbar')
  if (navbarElement) {
    navbarElement.addEventListener('mouseleave', handleNavbarMouseLeave)
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  const navbarElement = document.getElementById('navbar')
  if (navbarElement) {
    navbarElement.removeEventListener('mouseleave', handleNavbarMouseLeave)
  }
})

/* 侧边栏数据 */
const navItems = reactive([
  { icon: Home, text: '首页', path: '/home' },
  {
    icon: BookText,
    text: '知识中心',
    path: '/knowledge',
    submenu: [
      { icon: LibraryBig, text: '知识分类', path: '/knowledge/category' },
      { icon: Folder, text: '文件管理', path: '/knowledge/files' },
      { icon: LayoutPanelLeft, text: '知识图谱', path: '/knowledge/graph' }
    ]
  },
  { icon: ScanSearch, text: '知识搜寻', path: '/extraction' },
  { icon: MessageCircle, text: 'Support', path: '/support' },
  { icon: Settings, text: 'Settings', path: '/settings' }
])
</script>

<style scoped>
/* ===== CSS Variables ===== */
:root {
  --background-primary: 220, 13%, 18%;
  --background-secondary: 220, 13%, 15%;
  --background-secondary-dark: 220, 13%, 12%;
  --background-secondary-light: 220, 13%, 20%;
  --white: 0, 0%, 100%;
  --quite-gray: 0, 0%, 50%;
  --black: 0, 0%, 0%;
  --transition-main: 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* ===== Base Styles ===== */
* {
  box-sizing: border-box;
}

#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  background: hsl(var(--background-primary));
  color: hsl(var(--white));
  height: 100vh;
  overflow: hidden;
}

/* ===== Flexbox Utilities ===== */
.flexbox {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flexbox-left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flexbox-col {
  display: flex;
  flex-direction: column;
}

/* ===== Navbar ===== */
#navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 5em;
  height: 100vh;
  background:  hsl(var(--background-primary));
  transition: width var(--transition-main);
  overflow: visible;
  z-index: 1000;
}

#navbar::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 2em;
  height: 100%;
  z-index: -1;
}

#navbar:hover {
  width: 16em;
}

.navbar-items {
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
}

.navbar-logo {
  margin: 0 0 2em 0;
  height: 5em;
  background: hsl(var(--background-secondary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
  width: 5em;
  flex-shrink: 0;
}

.navbar-logo svg {
  fill: hsl(var(--white));
}
.navbar-item {
  padding: 0;
  cursor: pointer;
}

.navbar-item-inner {
  display: flex;
  align-items: center;
  padding: 1em;
  color: hsl(var(--quite-gray));
  border-radius: 0.25em;
  transition: all 0.3s ease-out;
  text-decoration: none;
  margin: 0 0.5em;
  position: relative;
  min-height: 3em;
  justify-content: flex-start;
}

.navbar-item-inner:hover,
.navbar-item-inner.active {
  color: hsl(var(--white));
  background: hsl(var(--background-secondary-light));
  box-shadow: 0 17px 30px -10px hsla(var(--black), 0.25);
}

.navbar-item-inner-icon-wrapper {
  width: 2.5em;
  height: 2.5em;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.link-text {
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-out 0.1s, visibility 0.3s ease-out 0.1s;
  text-align: left;
  font-weight: 500;
  margin-left: 1em;
}

#navbar:hover .link-text {
  opacity: 1;
  visibility: visible;
}

/* 子菜单相关样式 */
.navbar-item.has-submenu {
  flex-direction: column;
  align-items: stretch;
}

.navbar-item-inner.expanded {
  background: hsl(var(--background-secondary-light));
  color: hsl(var(--white));
}

.submenu-arrow {
  font-size: 10px;
  color: hsl(var(--quite-gray));
  transition: transform 0.3s ease-out;
  margin-left: auto;
  opacity: 0;
  visibility: hidden;
}

#navbar:hover .submenu-arrow {
  opacity: 1;
  visibility: visible;
}

.submenu-arrow.rotated {
  transform: rotate(180deg);
}

.submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  background: hsl(var(--background-secondary-dark));
}

.submenu.expanded {
  max-height: 200px;
  transition: max-height 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 当侧边栏不处于hover状态时，强制收缩所有子菜单 */
#navbar:not(:hover) .submenu {
  max-height: 0 !important;
  transition: max-height 0.25s cubic-bezier(0.4, 0, 0.6, 1) !important;
}

#navbar:not(:hover) .submenu-arrow {
  transform: rotate(0deg) !important;
  transition: transform 0.25s ease-out !important;
}

#navbar:not(:hover) .navbar-item-inner.expanded {
  background: transparent !important;
  color: hsl(var(--quite-gray)) !important;
  transition: all 0.3s ease-out 0.1s !important;
}

.submenu-item {
  padding: 0;
}

.submenu-item-inner {
  display: flex;
  align-items: center;
  padding: 0.75em 1em 0.75em 3.5em;
  color: hsl(var(--quite-gray));
  text-decoration: none;
  transition: all 0.3s ease-out;
  position: relative;
  min-height: 2.5em;
  justify-content: flex-start;
}

.submenu-item-inner:hover,
.submenu-item-inner.active {
  color: hsl(var(--white));
  background: hsl(var(--background-secondary-light));
}

.submenu-item-inner.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: hsl(var(--white));
}

.submenu-item-icon {
  width: 1.5em;
  height: 1.5em;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  margin-right: 0.75em;
}

.submenu-link-text {
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-out 0.1s, visibility 0.3s ease-out 0.1s;
  text-align: left;
  font-weight: 400;
  font-size: 0.9em;
}

#navbar:hover .submenu-link-text {
  opacity: 1;
  visibility: visible;
}

/* 当侧边栏收缩时，立即隐藏子菜单文字 */
#navbar:not(:hover) .submenu-link-text {
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity 0.15s ease-out !important;
}

/* 响应式设计中隐藏子菜单 */
@media (max-width: 768px) {
  .submenu {
    display: none;
  }
  
  .submenu-arrow {
    display: none;
  }
}

/* ===== Main Content ===== */
#main {
  margin-left: 5em;
  height: 100vh;
  padding: 2em;
  background: hsl(var(--background-primary));
  transition: margin-left var(--transition-main);
  overflow-y: auto;
}

#navbar:hover ~ #main {
  margin-left: 16em;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
  #navbar {
    width: 100%;
    height: auto;
    position: relative;
  }
  
  #navbar:hover {
    width: 100%;
  }
  
  #main {
    margin-left: 0;
  }
  
  #navbar:hover ~ #main {
    margin-left: 0;
  }
  
  .navbar-items {
    flex-direction: row;
    justify-content: space-around;
  }
  
  .navbar-logo {
    display: none;
  }

  
  .link-text {
    display: none;
  }
}

/* ===== 用户头像栏 ===== */
.user-profile {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: hsl(var(--background-secondary-dark));
  border-top: 1px solid hsl(var(--background-secondary-light));
  padding: 1em;
}

.user-profile-inner {
  display: flex;
  align-items: center;
  padding: 0.5em;
  border-radius: 0.5em;
  transition: background-color 0.3s ease-out;
  cursor: pointer;
  position: relative;
}

.user-profile-inner:hover,
.user-profile-inner.menu-active {
  background: hsl(var(--background-secondary-light));
}

.user-avatar {
  width: 2.5em;
  height: 2.5em;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background: hsl(var(--background-secondary-light));
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.user-name {
  margin-left: 1em;
  color: hsl(var(--white));
  font-weight: 500;
  font-size: 0.9em;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-out 0.1s, visibility 0.3s ease-out 0.1s;
}

#navbar:hover .user-name {
  opacity: 1;
  visibility: visible;
}

/* ===== 用户下拉菜单 ===== */
.user-dropdown-menu {
  position: absolute;
  bottom: calc(8%);
  left: calc(70% + 0.5em);
  background: hsl(var(--background-secondary));
  border: 1px solid hsl(var(--background-secondary-light));
  border-radius: 1.5em;
  padding: 0.5em 0;
  min-width: 120px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  transform: scale(0.9) translateY(10px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.user-dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: scale(1) translateY(0);
}

/* 当侧边栏收缩时，平滑隐藏下拉菜单 */
#navbar:not(:hover) .user-dropdown-menu {
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8) translateY(15px) translateX(-10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s;
}

/* 当侧边栏收缩时，菜单状态也要重置 */
#navbar:not(:hover) .user-profile-inner.menu-active {
  background: transparent;
  transition: background-color 0.4s ease-out 0.1s;
}

.dropdown-item {
  padding: 0.75em 1.5em;
  color: hsl(var(--quite-gray));
  cursor: pointer;
  transition: all 0.2s ease-out;
  font-size: 0.9em;
  font-weight: 500;
}

.dropdown-item:hover {
  background: hsl(var(--background-secondary-light));
  color: hsl(var(--white));
}

.dropdown-item:first-child {
  border-radius: 1.5em 1.5em 0 0;
}

.dropdown-item:last-child {
  border-radius: 0 0 1.5em 1.5em;
}

.dropdown-item:only-child {
  border-radius: 1.5em;
}

/* 调整导航项容器高度，为头像栏留出空间 */
.navbar-items {
  height: calc(100% - 5em);
  padding-bottom: 1em;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-profile {
    position: relative;
    padding: 0.5em;
  }
  
  .user-name {
    display: none;
  }
  
  .navbar-items {
    height: auto;
    padding-bottom: 0;
  }

  .user-dropdown-menu {
    left: 50%;
    transform: translateX(-50%) scale(0.9) translateY(10px);
    bottom: calc(100% + 0.5em);
  }

  .user-dropdown-menu.show {
    transform: translateX(-50%) scale(1) translateY(0);
  }
}

.sidebar-logo img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  display: block;
  margin-left: 25px;
  margin-top: 10px;
}
</style>























