<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;4bda2355-364d-4794-af24-7e8ca65aeb35&quot;,&quot;conversations&quot;:{&quot;4bda2355-364d-4794-af24-7e8ca65aeb35&quot;:{&quot;id&quot;:&quot;4bda2355-364d-4794-af24-7e8ca65aeb35&quot;,&quot;createdAtIso&quot;:&quot;2025-07-28T01:53:04.370Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-28T02:15:17.432Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;df7dddcb-f0a8-4058-8fa8-14b3dcbb315b&quot;,&quot;request_message&quot;:&quot;\n\n\n\n这是为什么&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;},{&quot;type&quot;:&quot;file&quot;,&quot;attrs&quot;:{&quot;src&quot;:&quot;a0c810be20dcee7b04e0bde40530730f96ac8dcf6de70081a9c3faefe62f6c99.png&quot;,&quot;alt&quot;:null,&quot;title&quot;:&quot;image.png&quot;,&quot;renderMode&quot;:&quot;collapsed&quot;,&quot;isLoading&quot;:false,&quot;fileSizeBytes&quot;:255269,&quot;mimeType&quot;:&quot;image/png&quot;}},{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;这是为什么&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;*You are out of user messages <NAME_EMAIL>. Please update your account [here](https://app.augmentcode.com/account) to continue using Augment.*\n\n---\n\n*Your access expires in 4 days. [Purchase a subscription](https://app.augmentcode.com/account)*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:3,&quot;image_id_node&quot;:{&quot;image_id&quot;:&quot;a0c810be20dcee7b04e0bde40530730f96ac8dcf6de70081a9c3faefe62f6c99.png&quot;,&quot;format&quot;:1}},{&quot;id&quot;:1,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;这是为什么&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/Users/<USER>/Desktop/best test&quot;,&quot;folderRoot&quot;:&quot;C:/Users/<USER>/Desktop/best test&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-28T02:07:05.790Z&quot;,&quot;response_text&quot;:&quot;*You are out of user messages <NAME_EMAIL>. Please update your account [here](https://app.augmentcode.com/account) to continue using Augment.*\n\n---\n\n*Your access expires in 4 days. [Purchase a subscription](https://app.augmentcode.com/account)*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-cc00f6f2-d003-4433-8531-2bd6082d529f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;d865ec6b-e351-4da3-ae98-df74864f87ef&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>