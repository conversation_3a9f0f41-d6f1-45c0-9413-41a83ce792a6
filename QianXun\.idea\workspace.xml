<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6f7b6897-0eb8-4834-9b24-41bd6f3390ac" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\apache-maven-3.6.3\repo" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="30AEE5C2NSE0rfWdU6g1yVNoUi7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;Spring Boot.QainXun.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.QianXunLatterApplication.executor&quot;: &quot;Run&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/best test/QianXun&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\IntelliJ IDEA 2025.1.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RunManager">
    <configuration name="QainXun" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="ALTERNATIVE_JRE_PATH" value="21" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="QianXun-latter" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.QianXunLatterApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="6f7b6897-0eb8-4834-9b24-41bd6f3390ac" name="更改" comment="" />
      <created>1753061861838</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753061861838</updated>
      <workItem from="1753061862888" duration="156000" />
      <workItem from="1753062077767" duration="90000" />
      <workItem from="1753062183057" duration="273000" />
      <workItem from="1753062501230" duration="2611000" />
      <workItem from="1753065144294" duration="76000" />
      <workItem from="1753065229831" duration="266000" />
      <workItem from="1753065524712" duration="4813000" />
      <workItem from="1753075308352" duration="4313000" />
      <workItem from="1753080862380" duration="8967000" />
      <workItem from="1753098328439" duration="3683000" />
      <workItem from="1753103553737" duration="1570000" />
      <workItem from="1753142406106" duration="5890000" />
      <workItem from="1753150090783" duration="23160000" />
      <workItem from="1753190673416" duration="1350000" />
      <workItem from="1753228548843" duration="11555000" />
      <workItem from="1753240555721" duration="5337000" />
      <workItem from="1753249828594" duration="2656000" />
      <workItem from="1753254884607" duration="10000" />
      <workItem from="1753274892439" duration="1302000" />
      <workItem from="1753419781962" duration="2094000" />
      <workItem from="1753423555418" duration="3807000" />
      <workItem from="1753487776097" duration="30297000" />
      <workItem from="1753580019164" duration="7947000" />
      <workItem from="1753597399181" duration="2880000" />
      <workItem from="1753602696086" duration="3562000" />
      <workItem from="1753624118913" duration="244000" />
      <workItem from="1753665431151" duration="541000" />
      <workItem from="1753669835277" duration="3812000" />
      <workItem from="1753677027552" duration="429000" />
      <workItem from="1753677470334" duration="5647000" />
      <workItem from="1753700048017" duration="12000" />
      <workItem from="1753700121168" duration="2040000" />
      <workItem from="1753703176036" duration="12000" />
      <workItem from="1753704600919" duration="763000" />
      <workItem from="1753706527428" duration="4234000" />
      <workItem from="1753746037369" duration="1322000" />
      <workItem from="1753748470017" duration="6141000" />
      <workItem from="1753765333920" duration="2495000" />
      <workItem from="1753767855941" duration="14609000" />
      <workItem from="1753797451384" duration="1375000" />
      <workItem from="1753832134506" duration="919000" />
      <workItem from="1753834682121" duration="829000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>