<template>
  <div class="main-layout">
    <SideBar />
    <div class="main-content">
      <div class="content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import SideBar from '../components/SideBar.vue'
</script>

<style scoped>
.main-layout {
  display: flex;
  flex-direction: row;
  width: 100vw;
  height: 100vh;
  background: #f5f6fa;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fff;
}
</style>