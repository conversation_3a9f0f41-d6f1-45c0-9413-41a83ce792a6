const path = require('path');

module.exports = {
  mode: 'production',
  entry: './src/qiankun.ts',
  output: {
    path: path.resolve(__dirname, 'public'),
    filename: '_next/static/chunks/qiankun.js',
    library: 'sun-app',
    libraryTarget: 'umd',
    globalObject: 'window',
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader', 'postcss-loader'],
      },
    ],
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
  },
  externals: {
    react: 'React',
    'react-dom': 'ReactDOM',
  },
};