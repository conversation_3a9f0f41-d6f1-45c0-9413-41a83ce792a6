{"name": "sun", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:qiankun": "next dev -p 3001", "build": "next build", "build:qiankun": "next build && npm run build:umd", "build:umd": "webpack --config webpack.qiankun.js", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/three": "^0.160.0", "gsap": "^3.13.0", "next": "15.4.4", "qiankun": "^2.10.16", "react": "19.1.0", "react-dom": "19.1.0", "three": "^0.160.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "ts-loader": "^9.5.1", "style-loader": "^3.3.3", "css-loader": "^6.8.1", "postcss-loader": "^7.3.3"}}