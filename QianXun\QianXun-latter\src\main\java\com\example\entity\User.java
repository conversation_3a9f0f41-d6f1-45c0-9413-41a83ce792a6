package com.example.entity;

public class User {
    private Integer sno;
    private Integer deptId;
    private String nickName;
    private String password;
    private String phonenumber;
    private String userRole;
    private String avatar;

    public Integer getSno() {return sno;}

    public void setSno(Integer sno) {
        this.sno = sno;
    }

    public Integer getdeptId() {
        return deptId;
    }

    public void setdeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getnickName() {
        return nickName;
    }

    public void setnickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getuserRole() {
        return userRole;
    }

    public void setuserRole(String userRole) {
        this.userRole = userRole;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
}
