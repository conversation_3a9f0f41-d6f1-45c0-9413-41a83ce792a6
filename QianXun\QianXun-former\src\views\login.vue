
<template>
  <div class="login-container" @click="startAnimation">
    <img src="@/assets/login/background.png" alt=""  class="background"/>
    <img src="@/assets/login/logo.png" alt="" class="logo"
         :style="{
    left: isAnimated ? logoFinalPosition.left : logoInitialPosition.left,
    top: isAnimated ? logoFinalPosition.top : logoInitialPosition.top,
    width: isAnimated ? logoFinalSize.width : logoInitialSize.width,
    height: isAnimated ? logoFinalSize.height : logoInitialSize.height
  }"
    >
    <img src="@/assets/login/Group 4.png" alt="" class="group4"
         :style="{
        left: groupRepositioned ? group4FinalPosition.left : group4InitialPosition.left,
        top: groupRepositioned ? group4FinalPosition.top : group4InitialPosition.top,
        width: groupRepositioned ? group4FinalSize.width : group4InitialSize.width,
        height: groupRepositioned ? group4FinalSize.height : group4InitialSize.height,
        opacity: groupFadeOut && !groupFadeIn ? 0 : (groupFadeIn ? 1 : 1)
      }"
    >
    <img src="@/assets/login/Group 5.png" alt="" class="group5"
         :style="{
        left: groupRepositioned ? group5FinalPosition.left : group5InitialPosition.left,
        top: groupRepositioned ? group5FinalPosition.top : group5InitialPosition.top,
        width: groupRepositioned ? group5FinalSize.width : group5InitialSize.width,
        height: groupRepositioned ? group5FinalSize.height : group5InitialSize.height,
        opacity: groupFadeOut && !groupFadeIn ? 0 : (groupFadeIn ? 1 : 1)
      }"
    >
    <img src="@/assets/login/Group 6.png" alt="" class="group6"
         :style="{
        left: groupRepositioned ? group6FinalPosition.left : group6InitialPosition.left,
        top: groupRepositioned ? group6FinalPosition.top : group6InitialPosition.top,
        width: groupRepositioned ? group6FinalSize.width : group6InitialSize.width,
        height: groupRepositioned ? group6FinalSize.height : group6InitialSize.height,
        opacity: groupFadeOut && !groupFadeIn ? 0 : (groupFadeIn ? 1 : 1)
      }"
    >
    <img src="@/assets/login/line.png" alt="" class="line"
         :style="{
      left: isAnimated ? lineFinalPosition.left : lineInitialPosition.left,
      top: isAnimated ? lineFinalPosition.top : lineInitialPosition.top
    }"
    >
    <img src="@/assets/login/Star 2.png" alt="" class="star2"
         :style="{
      left: isAnimated ? star2FinalPosition.left : star2InitialPosition.left,
      top: isAnimated ? star2FinalPosition.top : star2InitialPosition.top,
      transform: isAnimated ? star2FinalTransform : star2InitialTransform
    }"
    >
    <img src="@/assets/login/Group 19.png" alt="" class="group19"
         :style="{
        left: groupRepositioned ? group19FinalPosition.left : group19InitialPosition.left,
        top: groupRepositioned ? group19FinalPosition.top : group19InitialPosition.top,
        width: groupRepositioned ? group19FinalSize.width : group19InitialSize.width,
        height: groupRepositioned ? group19FinalSize.height : group19InitialSize.height,
        opacity: groupFadeOut && !groupFadeIn ? 0 : (groupFadeIn ? 1 : 1)
      }"
    >
    <img src="@/assets/login/Star 1.png" alt="" class="star1"
         :style="{
      left: isAnimated ? star1FinalPosition.left : star1InitialPosition.left,
      top: isAnimated ? star1FinalPosition.top : star1InitialPosition.top,
      transform: isAnimated ? star1FinalTransform : star1InitialTransform
    }"
    >
    <img src="@/assets/login/Group 8.png" alt="" class="group8"
         :style="{
        left: groupRepositioned ? group8FinalPosition.left : group8InitialPosition.left,
        top: groupRepositioned ? group8FinalPosition.top : group8InitialPosition.top,
        width: groupRepositioned ? group8FinalSize.width : group8InitialSize.width,
        height: groupRepositioned ? group8FinalSize.height : group8InitialSize.height,
        opacity: groupFadeOut && !groupFadeIn ? 0 : (groupFadeIn ? 1 : 1)
      }"
    >
    <img src="@/assets/login/Group 9.png" alt="" class="group9"
         :style="{
        left: groupRepositioned ? group9FinalPosition.left : group9InitialPosition.left,
        top: groupRepositioned ? group9FinalPosition.top : group9InitialPosition.top,
        width: groupRepositioned ? group9FinalSize.width : group9InitialSize.width,
        height: groupRepositioned ? group9FinalSize.height : group9InitialSize.height,
        opacity: groupFadeOut && !groupFadeIn ? 0 : (groupFadeIn ? 1 : 1)
      }"
    >
    <img
        src="@/assets/login/Rectangle 19.png"
        alt=""
        class="rectangle19"
        :style="{
      width: isAnimated ? finalSize.width : initialSize.width,
      height: isAnimated ? finalSize.height : initialSize.height,
      left: isAnimated ? finalPosition.left : initialPosition.left,
      top: isAnimated ? finalPosition.top : initialPosition.top
    }"
    >
    <img src="@/assets/login/QianXun.png" alt="" class="qianxun"
         :style="{
        left: isAnimated ? qianxunFinalPosition.left : qianxunInitialPosition.left,
        top: isAnimated ? qianxunFinalPosition.top : qianxunInitialPosition.top,
        width: isAnimated ? qianxunFinalSize.width : qianxunInitialSize.width,
        height: isAnimated ? qianxunFinalSize.height : qianxunInitialSize.height
      }"
    >
    <img src="@/assets/login/line.png" alt="" class="line2"
         :style="{
        left: isAnimated ? line2FinalPosition.left : line2InitialPosition.left,
        top: isAnimated ? line2FinalPosition.top : line2InitialPosition.top
      }"
    >
    <img src="@/assets/login/setting.png" alt="" class="setting"
         :style="{
      width: isAnimated ? settingFinalSize.width : settingInitialSize.width,
      height: isAnimated ? settingFinalSize.height : settingInitialSize.height,
      left: isAnimated ? settingFinalPosition.left : settingInitialPosition.left,
      top: isAnimated ? settingFinalPosition.top : settingInitialPosition.top,
      transform: isAnimated ? settingFinalTransform : settingInitialTransform
    }"
    >
    <img src="@/assets/login/QA.png" alt="" class="qa"
         :style="{
        width: isAnimated ? qaFinalSize.width : qaInitialSize.width,
        height: isAnimated ? qaFinalSize.height : qaInitialSize.height,
        left: isAnimated ? qaFinalPosition.left : qaInitialPosition.left,
        top: isAnimated ? qaFinalPosition.top : qaInitialPosition.top,
        transform: isAnimated ? qaFinalTransform : qaInitialTransform
      }"
    >
    <img src="@/assets/login/whiteline.png" alt="" class="whiteline"
         :style="{
      width: isAnimated ? whitelineFinalSize.width : whitelineInitialSize.width,
      height: isAnimated ? whitelineFinalSize.height : whitelineInitialSize.height,
      left: isAnimated ? whitelineFinalPosition.left : whitelineInitialPosition.left,
      top: isAnimated ? whitelineFinalPosition.top : whitelineInitialPosition.top,
      transform: isAnimated ? whitelineFinalTransform : whitelineInitialTransform
    }"
    >
    <img src="@/assets/login/label.png" alt="" class="label" @click="login"
         :style="{
      width: isAnimated ? labelFinalSize.width : labelInitialSize.width,
      height: isAnimated ? labelFinalSize.height : labelInitialSize.height,
      left: isAnimated ? labelFinalPosition.left : labelInitialPosition.left,
      top: isAnimated ? labelFinalPosition.top : labelInitialPosition.top
    }"
    >
    <img src="@/assets/login/Login.png" alt="" class="Login" @click="login"
         :style="{
      width: isAnimated ? loginFinalSize.width : loginInitialSize.width,
      height: isAnimated ? loginFinalSize.height : loginInitialSize.height,
      left: isAnimated ? loginFinalPosition.left : loginInitialPosition.left,
      top: isAnimated ? loginFinalPosition.top : loginInitialPosition.top
    }"
    >
    <div class="register"  @click="centerDialogVisible = true"
         :style="{
      width: isAnimated ? registerFinalSize.width : registerInitialSize.width,
      height: isAnimated ? registerFinalSize.height : registerInitialSize.height,
      left: isAnimated ? registerFinalPosition.left : registerInitialPosition.left,
      top: isAnimated ? registerFinalPosition.top : registerInitialPosition.top
    }"
    >忘记密码？</div>

    //设置忘记密码弹窗
    <el-dialog
        v-model="centerDialogVisible"
        title="Forget Password"
        width="500"
        align-center
        style="border-radius: 20px"
    >
      <el-form :model="form" label-width="100px" :rules="rules">
        <el-form-item label="工号" prop="sno">
          <el-input v-model="form.sno" style="width: 300px"/>
        </el-form-item>
        <el-form-item label="电话号码" prop="phone">
          <el-input v-model="form.phone" style="width: 300px"/>
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input v-model="form.password" type="password" show-password style="width: 300px"/>
        </el-form-item>
        <el-form-item label="确认密码" prop="againpassword">
          <el-input v-model="form.againpassword" type="password" show-password style="width: 300px"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="clean()" type="danger">取消</el-button>
          <el-button type="primary" @click="clean()">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <img src="@/assets/login/cat.png" alt="" class="cat"
         :style="{
        width: isAnimated ? catFinalSize.width : catInitialSize.width,
        height: isAnimated ? catFinalSize.height : catInitialSize.height,
        left: isAnimated ? catFinalPosition.left : catInitialPosition.left,
        top: isAnimated ? catFinalPosition.top : catInitialPosition.top
      }"
    >
    <img src="@/assets/login/amazing.png" alt="" class="amazing"
         :style="{
        width: isAnimated ? amazingFinalSize.width : amazingInitialSize.width,
        height: isAnimated ? amazingFinalSize.height : amazingInitialSize.height,
        left: isAnimated ? amazingFinalPosition.left : amazingInitialPosition.left,
        top: isAnimated ? amazingFinalPosition.top : amazingInitialPosition.top
      }"
    >
    <img src="@/assets/login/Star 3.png" alt="" class="star3"
         :style="{
      width: isAnimated ? star3FinalSize.width : star3InitialSize.width,
      height: isAnimated ? star3FinalSize.height : star3InitialSize.height,
      left: isAnimated ? star3FinalPosition.left : star3InitialPosition.left,
      top: isAnimated ? star3FinalPosition.top : star3InitialPosition.top,
      transform: isAnimated ? star3FinalTransform : star3InitialTransform
    }"
    >
    <img src="@/assets/login/Bag.png" alt="" class="bag"
         :style="{
      width: bagRepositioned ? bagFinalSize.width : bagInitialSize.width,
      height: bagRepositioned ? bagFinalSize.height : bagInitialSize.height,
      left: bagRepositioned ? bagFinalPosition.left : bagInitialPosition.left,
      top: bagRepositioned ? bagFinalPosition.top : bagInitialPosition.top,
      opacity: bagFadeOut && !bagFadeIn ? 0 : (bagFadeIn ? 1 : 1)
    }"
    >
    <img src="@/assets/login/logintext.png" alt="" class="logintext" @click="handleLogin"
         :style="{
      width: isAnimated ? logintextFinalSize.width : logintextInitialSize.width,
      height: isAnimated ? logintextFinalSize.height : logintextInitialSize.height,
      left: isAnimated ? logintextFinalPosition.left : logintextInitialPosition.left,
      top: isAnimated ? logintextFinalPosition.top : logintextInitialPosition.top
    }"
    >
    <img src="@/assets/login/ma.png" alt="" class="ma"
         :style="{
      width: maRepositioned ? maFinalSize.width : maInitialSize.width,
      height: maRepositioned ? maFinalSize.height : maInitialSize.height,
      left: maRepositioned ? maFinalPosition.left : maInitialPosition.left,
      top: maRepositioned ? maFinalPosition.top : maInitialPosition.top,
      opacity: maFadeOut && !maFadeIn ? 0 : (maFadeIn ? 1 : 1)
    }"
    >
    <img src="@/assets/login/Star 4.png" alt="" class="star4"
         :style="{
      width: isAnimated ? star4FinalSize.width : star4InitialSize.width,
      height: isAnimated ? star4FinalSize.height : star4InitialSize.height,
      left: isAnimated ? star4FinalPosition.left : star4InitialPosition.left,
      top: isAnimated ? star4FinalPosition.top : star4InitialPosition.top,
      transform: isAnimated ? star4FinalTransform : star4InitialTransform
    }"
    >
    <img src="@/assets/login/Star 6.png" alt="" class="star5"
         :style="{
      width: isAnimated ? star5FinalSize.width : star5InitialSize.width,
      height: isAnimated ? star5FinalSize.height : star5InitialSize.height,
      left: isAnimated ? star5FinalPosition.left : star5InitialPosition.left,
      top: isAnimated ? star5FinalPosition.top : star5InitialPosition.top,
      transform: isAnimated ? star5FinalTransform : star5InitialTransform
    }"
    >
    <img src="@/assets/login/Star 7.png" alt="" class="star6"
         :style="{
      width: isAnimated ? star6FinalSize.width : star6InitialSize.width,
      height: isAnimated ? star6FinalSize.height : star6InitialSize.height,
      left: isAnimated ? star6FinalPosition.left : star6InitialPosition.left,
      top: isAnimated ? star6FinalPosition.top : star6InitialPosition.top,
      transform: isAnimated ? star6FinalTransform : star6InitialTransform
    }"
    >
    <img src="@/assets/login/Star 5.png" alt="" class="star7"
         :style="{
      width: isAnimated ? star7FinalSize.width : star7InitialSize.width,
      height: isAnimated ? star7FinalSize.height : star7InitialSize.height,
      left: isAnimated ? star7FinalPosition.left : star7InitialPosition.left,
      top: isAnimated ? star7FinalPosition.top : star7InitialPosition.top,
      transform: isAnimated ? star7FinalTransform : star7InitialTransform
    }"
    >
    <img src="@/assets/login/Star 3.png" alt="" class="star8"
         :style="{
      width: isAnimated ? star8FinalSize.width : star8InitialSize.width,
      height: isAnimated ? star8FinalSize.height : star8InitialSize.height,
      left: isAnimated ? star8FinalPosition.left : star8InitialPosition.left,
      top: isAnimated ? star8FinalPosition.top : star8InitialPosition.top,
      transform: isAnimated ? star8FinalTransform : star8InitialTransform
    }"
    >
    <div class="sno"
         :style="{
      width: isAnimated ? snoFinalSize.width : snoInitialSize.width,
      height: isAnimated ? snoFinalSize.height : snoInitialSize.height,
      left: isAnimated ? snoFinalPosition.left : snoInitialPosition.left,
      top: isAnimated ? snoFinalPosition.top : snoInitialPosition.top
    }"
    >工号</div>
    <img src="@/assets/login/input.png" alt="" class="input"
         :style="{
      width: isAnimated ? inputFinalSize.width : inputInitialSize.width,
      height: isAnimated ? inputFinalSize.height : inputInitialSize.height,
      left: isAnimated ? inputFinalPosition.left : inputInitialPosition.left,
      top: isAnimated ? inputFinalPosition.top : inputInitialPosition.top
    }"
         @click="handleInputClick"
    >
    <!-- 真实输入框1 -->
    <input
        v-if="showInput1"
        v-model="sno"
        type="text"
        class="real-input-1"
        :style="{
        position: 'absolute',
        width: `calc(${isAnimated ? inputFinalSize.width : inputInitialSize.width} -5px)`,
        height: isAnimated ? inputFinalSize.height : inputInitialSize.height,
        left: isAnimated ? inputFinalPosition.left : inputInitialPosition.left,
        top: `calc(${isAnimated ? inputFinalPosition.top : inputInitialPosition.top} + 2px)`,
        background: 'transparent',
        border: 'none',
        outline: 'none',
        boxShadow: 'none',
        padding: '8px 12px',
        fontSize: '14px',
        color: '#ffffff',
        zIndex: 1000
      }"
        @blur="handleInput1Blur"

    >
    <div class="phonenumber"
         :style="{
      width: isAnimated ? phonenumberFinalSize.width : phonenumberInitialSize.width,
      height: isAnimated ? phonenumberFinalSize.height : phonenumberInitialSize.height,
      left: isAnimated ? phonenumberFinalPosition.left : phonenumberInitialPosition.left,
      top: isAnimated ? phonenumberFinalPosition.top : phonenumberInitialPosition.top
    }"
    >密码</div>
    <img src="@/assets/login/input.png" alt="" class="input2"
         :style="{
      width: isAnimated ? input2FinalSize.width : input2InitialSize.width,
      height: isAnimated ? input2FinalSize.height : input2InitialSize.height,
      left: isAnimated ? input2FinalPosition.left : input2InitialPosition.left,
      top: isAnimated ? input2FinalPosition.top : input2InitialPosition.top
    }"
         @click="handleInput2Click"
    >
    <!-- 真实输入框2 -->
    <input
        v-if="showInput2"
        v-model="password"
        type="password"
        class="real-input-2"
        show-password
        :style="{
        position: 'absolute',
        width: `calc(${isAnimated ? input2FinalSize.width : input2InitialSize.width} - 13px)`,
        height: isAnimated ? input2FinalSize.height : input2InitialSize.height,
        left: isAnimated ? input2FinalPosition.left : input2InitialPosition.left,
        top: `calc(${isAnimated ? input2FinalPosition.top : input2InitialPosition.top} + 2px)`,
        background: 'transparent',
        border: 'none',
        outline: 'none',
        boxShadow: 'none',
        padding: '8px 12px',
        fontSize: '14px',
        color: '#ffffff',
        zIndex: 1000,
        
      }"
        @blur="handleInput2Blur"
    />

  </div>
</template>

<script setup>
import {ref, onMounted, nextTick, reactive, computed} from 'vue'
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";

//账号密码绑定
const sno = ref('')
const password = ref('')

const data = reactive({
  UserList: []
})

const rules = reactive({
  sno: [
    { required: true, message: '请输入工号', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入电话号码', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
  ],
  againpassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
  ],
})

const login = () =>{
  request.post("/user/login",{
    sno: sno.value,
    password: password.value
  }).then(res=>{
    if (res.code === '200'){
      //登录成功
      ElMessage.success("登录成功")
      location.href="/home"
    }else if(res.code === '400'){
      ElMessage.error(res.msg)
    }else {
      ElMessage.error(res.msg)
    }
  })
}


//忘记密码弹窗
const centerDialogVisible = ref(false)
const form = reactive({
  sno: '',
  phone: '',
  password: '',
  againpassword: '',
})
//清除忘记密码中写好的账号密码
const clean = () =>{
  form.sno = ''
  form.phone = ''
  form.password = ''
  form.againpassword = ''
  centerDialogVisible.value = false
}


// Animation state
const isAnimated = ref(false)
const animationStarted = ref(false)

// 点击开始动画
const startAnimation = () => {
  if (animationStarted.value) return // 防止重复触发

  animationStarted.value = true

  // All elements start animation together
  isAnimated.value = true

  // Group elements, bag and ma fade out immediately
  groupFadeState.value = true
  groupFadeOut.value = true
  bagFadeOut.value = true
  maFadeOut.value = true

  // After fade out completes, instantly move to new positions
  setTimeout(() => {
    groupRepositioned.value = true
    bagRepositioned.value = true
    maRepositioned.value = true

    // Immediately fade in at new positions
    setTimeout(() => {
      groupFadeIn.value = true
      bagFadeIn.value = true
      maFadeIn.value = true
    }, 50)
  }, 375)
}

// 移动到右下角的偏移量
const offsetRight = 0
const offsetBottom = 0

// 计算移动后的最终位置
const moveBottomRight = (position) => {
  const left = position.left
  const top = position.top
  
  // 解析数值和单位
  const parsePosition = (pos, offset) => {
    if (pos.includes('px')) {
      const value = parseFloat(pos)
      return `${value + offset}px`
    } else if (pos.includes('%')) {
      return pos // 保持百分比不变
    } else {
      return pos // 其他情况保持原值
    }
  }
  
  return {
    left: parsePosition(left, offsetRight),
    top: parsePosition(top, offsetBottom)
  }
}

// Logo animation values
const logoInitialPosition = {
  left: 'calc(660/ 1920 * 100vw)',
  top: 'calc(179/1080*100vh)'
}
const logoFinalPosition = computed(() => {
  return moveBottomRight({
    left: '605px',
    top: '240px'
  })
})
const logoInitialSize = {
  width: '60px',
  height: 'auto'
}
const logoFinalSize = {
  width: '45px',
  height: 'auto'
}

// Rectangle19 animation values
const initialPosition = {
  left: 'calc(616/ 1920 * 100vw)',
  top: 'calc(770 /1080 * 100vh)'
}
const initialSize = {
  width: '80px',
  height: '50px'
}
const finalPosition = {
  left: '50%',
  top: '50%'
}
const finalSize = {
  width: '750px',
  height: '400px'
}

// Whiteline element animation values
const whitelineInitialPosition = {
  left: 'calc(620/ 1920 * 100vw)',
  top: 'calc(940/ 1080 * 100vh)'
}
const whitelineInitialSize = {
  width: '300px',
  height: 'auto'
}
const whitelineInitialTransform = 'rotate(0deg)'

const whitelineFinalPosition = {
  left: '50%',
  top: '50%'
}
const whitelineFinalSize = {
  width: '300px',
  height: 'auto'
}
const whitelineFinalTransform = 'translate(-50%, -50%) rotate(86deg)'

// Line animation values
const lineInitialPosition = {
  left: 'calc(436 / 1920 * 100vw)',
  top: 'calc(316 / 1080 * 100vh)'
}
const lineFinalPosition = computed(() => {
  return moveBottomRight({
    left: '500px',
    top: '250px'
  })
})

// QianXun animation values
const qianxunInitialPosition = {
  left: 'calc(810/ 1920 * 100vw)',
  top: 'calc(230 /1080 * 100vh)'
}
const qianxunFinalPosition = computed(() => {
  return moveBottomRight({
    left: '670px',
    top: '245px'
  })
})
const qianxunInitialSize = {
  width: 'auto',
  height: 'auto'
}
const qianxunFinalSize = {
  width: '60px',
  height: 'auto'
}

// Line2 animation values
const line2InitialPosition = {
  left: 'calc(900/ 1920 * 100vw)',
  top: 'calc(267 /1080 * 100vh)'
}
const line2FinalPosition = computed(() => {
  return moveBottomRight({
    left: '740px',
    top: '250px'
  })
})

// Star1 animation values
const star1InitialPosition = {
  left: 'calc(370/ 1920 * 100vw)',
  top: 'calc(530/ 1080 * 100vh)'
}
const star1FinalPosition = computed(() => {
  return moveBottomRight({
    left: '490px',
    top: '270px'
  })
})
const star1InitialTransform = 'rotate(0deg)'
const star1FinalTransform = 'rotate(360deg)'

// Group elements animation values - fade out and fade in effect
// Final positions: 2x3 grid layout below logo

// Group4 animation values (top row, left)
const group4InitialPosition = {
  left: 'calc(439/ 1920 * 100vw)',
  top: 'calc(186.61 / 1080 * 100vh)'
}
const group4FinalPosition = computed(() => {
  return moveBottomRight({
    left: '555px',
    top: '325px'
  })
})
const group4InitialSize = {
  width: 'auto',
  height: 'auto'
}
const group4FinalSize = {
  width: '100px',
  height: 'auto'
}
const group4InitialOpacity = 1
const group4FinalOpacity = 1

// Group5 animation values (top row, right)
const group5InitialPosition = {
  left: 'calc(1100 / 1920 * 100vw)',
  top: 'calc(211 / 1080 * 100vh)'
}
const group5FinalPosition = computed(() => {
  return moveBottomRight({
    left: '675px',
    top: '325px'
  })
})
const group5InitialSize = {
  width: 'auto',
  height: 'auto'
}
const group5FinalSize = {
  width: '100px',
  height: 'auto'
}
const group5InitialOpacity = 1
const group5FinalOpacity = 1

// Group6 animation values (middle row, right)
const group6InitialPosition = {
  left: 'calc(303 / 1920 * 100vw)',
  top: 'calc(294 / 1080 * 100vh)'
}
const group6FinalPosition = computed(() => {
  return moveBottomRight({
    left: '675px',
    top: '395px'
  })
})
const group6InitialSize = {
  width: 'auto',
  height: 'auto'
}
const group6FinalSize = {
  width: '100px',
  height: 'auto'
}
const group6InitialOpacity = 1
const group6FinalOpacity = 1

// Group19 animation values (middle row, left)
const group19InitialPosition = {
  left: 'calc(295/ 1920 * 100vw)',
  top: 'calc(450/ 1080 * 100vh)'
}
const group19FinalPosition = computed(() => {
  return moveBottomRight({
    left: '555px',
    top: '395px'
  })
})
const group19InitialSize = {
  width: 'auto',
  height: 'auto'
}
const group19FinalSize = {
  width: '100px',
  height: 'auto'
}
const group19InitialOpacity = 1
const group19FinalOpacity = 1

// Group8 animation values (bottom row, left)
const group8InitialPosition = {
  left: 'calc(343/ 1920 * 100vw)',
  top: 'calc(660/ 1080 * 100vh)'
}
const group8FinalPosition = computed(() => {
  return moveBottomRight({
    left: '555px',
    top: '465px'
  })
})
const group8InitialSize = {
  width: 'auto',
  height: 'auto'
}
const group8FinalSize = {
  width: '100px',
  height: 'auto'
}
const group8InitialOpacity = 1
const group8FinalOpacity = 1

// Group9 animation values (bottom row, right)
const group9InitialPosition = {
  left: 'calc(350/ 1920 * 100vw)',
  top: 'calc(800/ 1080 * 100vh)'
}
const group9FinalPosition = computed(() => {
  return moveBottomRight({
    left: '675px',
    top: '465px'
  })
})
const group9InitialSize = {
  width: 'auto',
  height: 'auto'
}
const group9FinalSize = {
  width: '100px',
  height: 'auto'
}
const group9InitialOpacity = 1
const group9FinalOpacity = 1

// Star2 animation values
const star2InitialPosition = {
  left: 'calc(600/ 1920 * 100vw)',
  top: 'calc(290 / 1080 * 100vh)'
}
const star2FinalPosition = computed(() => {
  return moveBottomRight({
    left: '540px',
    top: '270px'
  })
})
const star2InitialTransform = 'rotate(0deg)'
const star2FinalTransform = 'rotate(-270deg)'

// Setting animation values
const settingInitialPosition = {
  left: 'calc(508/ 1920 * 100vw)',
  top: 'calc(850/ 1080 * 100vh)'
}
const settingInitialSize = {
  width: '150px',
  height: 'auto'
}
const settingInitialTransform = 'rotate(0deg)'

const settingFinalPosition = computed(() => {
  return moveBottomRight({
    left: '440px',
    top: '430px'
  })
})
const settingFinalSize = {
  width: '200px',
  height: 'auto'
}
const settingFinalTransform = 'rotate(45deg)'

// Animation states for fade effect
const groupFadeState = ref(false)
const groupFadeOut = ref(false)
const groupRepositioned = ref(false)
const groupFadeIn = ref(false)

// Bag animation values
const bagInitialPosition = {
  left: 'calc(1100/ 1920 * 100vw)',
  top: 'calc(1000/ 1080 * 100vh)'
}
const bagInitialSize = {
  width: '50px',
  height: '50px'
}
const bagInitialOpacity = 1

const bagFinalPosition = computed(() => {
  return moveBottomRight({
    left: '690px',
    top: '530px'
  })
})
const bagFinalSize = {
  width: '70px',
  height: '70px'
}
const bagFinalOpacity = 1

// Bag animation states
const bagFadeOut = ref(false)
const bagRepositioned = ref(false)
const bagFadeIn = ref(false)

// QA animation values
const qaInitialPosition = {
  left: 'calc(900/ 1920 * 100vw)',
  top: 'calc(850/ 1080 * 100vh)'
}
const qaInitialSize = {
  width: '130px',
  height: 'auto'
}
const qaInitialTransform = 'rotate(0deg)'

const qaFinalPosition = computed(() => {
  return moveBottomRight({
    left: '760px',
    top: '360px'
  })
})
const qaFinalSize = {
  width: '130px',
  height: 'auto'
}
const qaFinalTransform = 'rotate(180deg)'

// Cat animation values
const catInitialPosition = {
  left: 'calc(1060/ 1920 * 100vw)',
  top: 'calc(610/ 1080 * 100vh)'
}
const catInitialSize = {
  width: '200px',
  height: 'auto'
}

const catFinalPosition = computed(() => {
  return moveBottomRight({
    left: '1100px',
    top: '569px'
  })
})
const catFinalSize = {
  width: '50px',
  height: 'auto'
}

// Amazing animation values
const amazingInitialPosition = {
  left: 'calc(1280/ 1920 * 100vw)',
  top: 'calc(490/ 1080 * 100vh)'
}
const amazingInitialSize = {
  width: 'auto',
  height: 'auto'
}

const amazingFinalPosition = computed(() => {
  return moveBottomRight({
    left: '1145px',
    top: '550px'
  })
})
const amazingFinalSize = {
  width: 'auto',
  height: '30px'
}

// Logintext animation values
const logintextInitialPosition = {
  left: 'calc(1500/ 1920 * 100vw)',
  top: 'calc(200/ 1080 * 100vh)'
}
const logintextInitialSize = {
  width: 'auto',
  height: 'auto'
}

const logintextFinalPosition = computed(() => {
  return moveBottomRight({
    left: '970px',
    top: '230px'
  })
})
const logintextFinalSize = {
  width: 'auto',
  height: 'auto'
}

// Star5 animation values
const star5InitialPosition = {
  left: 'calc(1600/ 1920 * 100vw)',
  top: 'calc(480/ 1080 * 100vh)'
}
const star5InitialSize = {
  width: '10px',
  height: 'auto'
}
const star5InitialTransform = 'rotate(0deg)'

const star5FinalPosition = computed(() => {
  return moveBottomRight({
    left: '860px',
    top: '220px'
  })
})
const star5FinalSize = {
  width: 'auto',
  height: 'auto'
}
const star5FinalTransform = 'rotate(360deg)'

// Star7 animation values
const star7InitialPosition = {
  left: 'calc(1590/ 1920 * 100vw)',
  top: 'calc(560/ 1080 * 100vh)'
}
const star7InitialSize = {
  width: '15px',
  height: 'auto'
}
const star7InitialTransform = 'rotate(0deg)'

const star7FinalPosition = computed(() => {
  return moveBottomRight({
    left: '910px',
    top: '260px'
  })
})
const star7FinalSize = {
  width: '15px',
  height: 'auto'
}
const star7FinalTransform = 'rotate(360deg)'

// Star3 animation values
const star3InitialPosition = {
  left: 'calc(1250/ 1920 * 100vw)',
  top: 'calc(393/ 1080 * 100vh)'
}
const star3InitialSize = {
  width: '20px',
  height: '20px'
}
const star3InitialTransform = 'rotate(0deg)'

const star3FinalPosition = computed(() => {
  return moveBottomRight({
    left: '1070px',
    top: '255px'
  })
})
const star3FinalSize = {
  width: '20px',
  height: '20px'
}
const star3FinalTransform = 'rotate(360deg)'

// Star4 animation values
const star4InitialPosition = {
  left: 'calc(1580/ 1920 * 100vw)',
  top: 'calc(430/ 1080 * 100vh)'
}
const star4InitialSize = {
  width: '10px',
  height: 'auto'
}
const star4InitialTransform = 'rotate(0deg)'

const star4FinalPosition = computed(() => {
  return moveBottomRight({
    left: '935px',
    top: '255px'
  })
})
const star4FinalSize = {
  width: '20px',
  height: 'auto'
}
const star4FinalTransform = 'rotate(360deg)'

// Star6 animation values
const star6InitialPosition = {
  left: 'calc(1610/ 1920 * 100vw)',
  top: 'calc(500/ 1080 * 100vh)'
}
const star6InitialSize = {
  width: '30px',
  height: 'auto'
}
const star6InitialTransform = 'rotate(0deg)'

const star6FinalPosition = computed(() => {
  return moveBottomRight({
    left: '1090px',
    top: '245px'
  })
})
const star6FinalSize = {
  width: '30px',
  height: 'auto'
}
const star6FinalTransform = 'rotate(45deg)'

// Star8 animation values
const star8InitialPosition = {
  left: 'calc(1650/ 1920 * 100vw)',
  top: 'calc(450/ 1080 * 100vh)'
}
const star8InitialSize = {
  width: '30px',
  height: 'auto'
}
const star8InitialTransform = 'rotate(0deg)'

const star8FinalPosition = computed(() => {
  return moveBottomRight({
    left: '1125px',
    top: '220px'
  })
})
const star8FinalSize = {
  width: '52px',
  height: '54px'
}
const star8FinalTransform = 'rotate(360deg)'

// Input animation values
const inputInitialPosition = {
  left: 'calc(1503/ 1920 * 100vw)',
  top: 'calc(570/ 1080 * 100vh)'
}
const inputInitialSize = {
  width: '200px',
  height: 'auto'
}

const inputFinalPosition = computed(() => {
  return moveBottomRight({
    left: '910px',
    top: '350px'
  })
})
const inputFinalSize = {
  width: '240px',
  height: 'auto'
}

// Sno animation values
const snoInitialPosition = {
  left: 'calc(1403/ 1920 * 100vw)',
  top: 'calc(530/ 1080 * 100vh)'
}
const snoInitialSize = {
  width: 'auto',
  height: 'auto'
}

const snoFinalPosition = computed(() => {
  return moveBottomRight({
    left: '915px',
    top: '325px'
  })
})
const snoFinalSize = {
  width: 'auto',
  height: 'auto'
}

// Phonenumber animation values
const phonenumberInitialPosition = {
  left: 'calc(1393/ 1920 * 100vw)',
  top: 'calc((730/ 1080 * 100vh) - 20px)'
}
const phonenumberInitialSize = {
  width: '40px',
  height: 'auto'
}

const phonenumberFinalPosition = computed(() => {
  return moveBottomRight({
    left: '915px',
    top: '405px'
  })
})
const phonenumberFinalSize = {
  width: '45px',
  height: 'auto'
}

// Input2 animation values
const input2InitialPosition = {
  left: 'calc(1383/ 1920 * 100vw)',
  top: 'calc(750/ 1080 * 100vh)'
}
const input2InitialSize = {
  width: '240px',
  height: 'auto'
}

const input2FinalPosition = computed(() => {
  return moveBottomRight({
    left: '910px',
    top: '430px'
  })
})
const input2FinalSize = {
  width: '240px',
  height: 'auto'
}

// Ma animation values
const maInitialPosition = {
  left: 'calc(1530/ 1920 * 100vw)',
  top: 'calc(350/ 1080 * 100vh)'
}
const maInitialSize = {
  width: '50px',
  height: 'auto'
}

const maFinalPosition = computed(() => {
  return moveBottomRight({
    left: '1165px',
    top: '215px'
  })
})
const maFinalSize = {
  width: '45px',
  height: 'auto'
}

// Ma animation states
const maFadeOut = ref(false)
const maRepositioned = ref(false)
const maFadeIn = ref(false)

// Label animation values
const labelInitialPosition = {
  left: 'calc(900/ 1920 * 100vw)',
  top: 'calc(840/ 1080 * 100vh)'
}
const labelInitialSize = {
  width: '170px',
  height: 'auto'
}

const labelFinalPosition = computed(() => {
  return moveBottomRight({
    left: '910px',
    top: '500px'
  })
})
const labelFinalSize = {
  width: '170px',
  height: 'auto'
}

// Login animation values
const loginInitialPosition = {
  left: 'calc(1010/ 1920 * 100vw)',
  top: 'calc(810/ 1080 * 100vh)'
}
const loginInitialSize = {
  width: 'auto',
  height: 'auto'
}

const loginFinalPosition = computed(() => {
  return moveBottomRight({
    left: '974px',
    top: '505px'
  })
})
const loginFinalSize = {
  width: 'auto',
  height: 'auto'
}

// Register animation values
const registerInitialPosition = {
  left: 'calc(1120/ 1920 * 100vw)',
  top: 'calc(810/ 1080 * 100vh)'
}
const registerInitialSize = {
  width: 'auto',
  height: 'auto'
}

const registerFinalPosition = computed(() => {
  return moveBottomRight({
    left: '1100px',
    top: '505px'
  })
})
const registerFinalSize = {
  width: 'auto',
  height: 'auto'
}

// 输入框显示状态和值
const showInput1 = ref(false)
const showInput2 = ref(false)
const inputValue1 = ref('')
const inputValue2 = ref('')

// 点击input图片显示输入框
const handleInputClick = async () => {
  showInput1.value = true
  await nextTick()
  const inputEl = document.querySelector('.real-input-1')
  if (inputEl) inputEl.focus()
}

// 点击input2图片显示输入框
const handleInput2Click = async () => {
  showInput2.value = true
  await nextTick()
  const inputEl = document.querySelector('.real-input-2')
  if (inputEl) inputEl.focus()
}

// 输入框失去焦点时的处理
const handleInput1Blur = () => {
  // 可选择隐藏或保持显示，这里选择保持显示
  // showInput1.value = false
}

const handleInput2Blur = () => {
  // 可选择隐藏或保持显示，这里选择保持显示
  // showInput2.value = false
}

// Start animation on component mount
onMounted(() => {
  // 移除自动开始动画的代码
})

</script>

<style scoped>
.line{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.logo{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.background{
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -100;
}
.group4{
  position: absolute;
  transition: opacity 0.375s ease-in-out;
}
.group5{
  position: absolute;
  transition: opacity 0.375s ease-in-out;
}
.group6{
  position: absolute;
  transition: opacity 0.375s ease-in-out;
}
.group19{
  position: absolute;
  transition: opacity 0.375s ease-in-out;
}
.group8{
  position: absolute;
  transition: opacity 0.375s ease-in-out;
}
.group9{
  position: absolute;
  transition: opacity 0.375s ease-in-out;
}
.rectangle19{
  z-index: -10;
  position: absolute;
  transform-origin: bottom left;
  transition: all 0.75s ease-in-out;
  transform: translate(-50%, -50%);
}
.qianxun{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.line2{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.setting{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.qa{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.whiteline{
  position: absolute;
  transition: all 0.75s ease-in-out;
  transform-origin: center;
}
.label{
  height: auto;
  width: 170px;
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.Login{
  position: absolute;
  left: calc(1010/ 1920 * 100vw);
  top:calc(810/ 1080 * 100vh);
  transition: all 0.75s ease-in-out;
}
.register{
  color: white;
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.cat{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.amazing{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.star3{
  height: 20px;
  width: 20px;
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.bag{
  position: absolute;
  transition: opacity 0.375s ease-in-out;
}
.logintext{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.ma{
  height: auto;
  width: 50px;
  position: absolute;
  transition: opacity 0.375s ease-in-out;
}
.star4{
  height: auto;
  width: 10px;
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.star5{
  height: auto;
  width: 10px;
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.star6{
  height: auto;
  width: 30px;
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.star7{
  height: auto;
  width: 15px;
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.star8{
  height: auto;
  width: 30px;
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.sno{
  color: white;
  height: auto;
  position: absolute;
  transition: all 0.75s ease-in-out;
}

.input{
  position: absolute;
  transition: all 0.75s ease-in-out;
  cursor: pointer;
}
.phonenumber{
  color: white;
  height: auto;
  width: 30px;
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.input2{
  position: absolute;
  transition: all 0.75s ease-in-out;
  cursor: pointer;
}
.star1{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.star2{
  position: absolute;
  transition: all 0.75s ease-in-out;
}
.real-input-1, .real-input-2 {
  transition: all 0.75s ease-in-out;
  outline: none;
}

.login-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  cursor: pointer;
}

@keyframes moveToBottomRight {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(20px, 20px);
  }
}



</style>
